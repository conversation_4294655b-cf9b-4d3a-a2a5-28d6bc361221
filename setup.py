"""
Setup script for AI Function Call Pipeline.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="ai-function-call-pipeline",
    version="1.0.0",
    description="AI-powered pipeline for converting natural language queries into structured function call sequences",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AI Function Call Pipeline Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/ai-function-call-pipeline",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "gpu": [
            "torch[cuda]>=2.0.0",
        ],
        "full": [
            "spacy>=3.6.0",
            "nltk>=3.8.0",
            "requests>=2.28.0",
            "beautifulsoup4>=4.12.0",
            "matplotlib>=3.7.0",
            "plotly>=5.15.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-pipeline=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="ai, nlp, function-calling, pipeline, automation, llm",
    project_urls={
        "Documentation": "https://github.com/your-org/ai-function-call-pipeline/docs",
        "Source": "https://github.com/your-org/ai-function-call-pipeline",
        "Tracker": "https://github.com/your-org/ai-function-call-pipeline/issues",
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt"],
    },
)
