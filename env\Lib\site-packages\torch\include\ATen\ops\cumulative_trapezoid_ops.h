#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API cumulative_trapezoid_x {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::cumulative_trapezoid";
  static constexpr const char* overload_name = "x";
  static constexpr const char* schema_str = "cumulative_trapezoid.x(Tensor y, Tensor x, *, int dim=-1) -> Tensor";
  static at::Tensor call(const at::Tensor & y, const at::Tensor & x, int64_t dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & y, const at::Tensor & x, int64_t dim);
};

struct TORCH_API cumulative_trapezoid_dx {
  using schema = at::Tensor (const at::Tensor &, const at::Scalar &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::cumulative_trapezoid";
  static constexpr const char* overload_name = "dx";
  static constexpr const char* schema_str = "cumulative_trapezoid.dx(Tensor y, *, Scalar dx=1, int dim=-1) -> Tensor";
  static at::Tensor call(const at::Tensor & y, const at::Scalar & dx, int64_t dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & y, const at::Scalar & dx, int64_t dim);
};

}} // namespace at::_ops
