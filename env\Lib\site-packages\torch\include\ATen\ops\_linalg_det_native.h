#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/_linalg_det_meta.h>

namespace at {
namespace native {
struct TORCH_API structured__linalg_det_out : public at::meta::structured__linalg_det {
void impl(const at::Tensor & A, const at::Tensor & result, const at::Tensor & LU, const at::Tensor & pivots);
};
} // namespace native
} // namespace at
