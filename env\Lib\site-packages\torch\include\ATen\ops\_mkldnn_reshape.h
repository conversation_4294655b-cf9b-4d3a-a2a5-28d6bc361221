#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_mkldnn_reshape_ops.h>

namespace at {


// aten::_mkldnn_reshape(Tensor self, int[] shape) -> Tensor
inline at::Tensor _mkldnn_reshape(const at::Tensor & self, at::IntArrayRef shape) {
    return at::_ops::_mkldnn_reshape::call(self, shape);
}

// aten::_mkldnn_reshape.out(Tensor self, int[] shape, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _mkldnn_reshape_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef shape) {
    return at::_ops::_mkldnn_reshape_out::call(self, shape, out);
}
// aten::_mkldnn_reshape.out(Tensor self, int[] shape, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _mkldnn_reshape_outf(const at::Tensor & self, at::IntArrayRef shape, at::Tensor & out) {
    return at::_ops::_mkldnn_reshape_out::call(self, shape, out);
}

}
