# AI Function Call Pipeline

A sophisticated pipeline that leverages open-source AI models to process natural language queries and generate structured sequences of function calls.

## Overview

This project creates an intelligent pipeline that:
- Interprets natural language user queries
- Breaks down complex requests into discrete tasks
- Selects appropriate functions from a comprehensive library
- Returns logically structured execution flows with proper input/output mapping

## Features

- **Open-Source AI Integration**: Uses models like Mistral-7B or similar for query processing
- **Comprehensive Function Library**: ~50 predefined functions covering various domains
- **Intelligent Query Parsing**: Advanced natural language understanding
- **Execution Flow Generation**: Automatic sequencing with dependency management
- **Extensible Architecture**: Easy to add new functions and capabilities

## Project Structure

```
├── src/
│   ├── pipeline/           # Core pipeline implementation
│   ├── functions/          # Function library definitions
│   ├── models/            # AI model integration
│   ├── parsers/           # Query parsing components
│   └── utils/             # Utility functions
├── examples/              # Example queries and usage
├── tests/                 # Test suite
├── config/               # Configuration files
└── docs/                 # Documentation
```

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd ai-function-call-pipeline

# Install dependencies
pip install -r requirements.txt

# Run basic example
python examples/basic_example.py

# Or use the command-line interface
python main.py "Get all invoices for March and send summary to my email"
```

### Command Line Usage

```bash
# Process a single query
python main.py "Analyze sales data and create a report"

# Interactive mode
python main.py --interactive

# List available functions
python main.py --list-functions

# Search for specific functions
python main.py --search-functions "email"

# Run example queries
python main.py --examples

# Use different model
python main.py --model phi-3 "Your query here"

# Enable actual execution (use with caution)
python main.py --execute "Your query here"
```

### Python API Usage

```python
from src.pipeline.core import FunctionCallPipeline, PipelineBuilder
from src.models.model_interface import DEFAULT_MODELS

# Create pipeline
pipeline = (PipelineBuilder()
           .with_model(DEFAULT_MODELS["mistral-7b"])
           .with_execution(False)  # Safe mode
           .build())

# Process query
query = "Retrieve all invoices for March, summarize the total amount, and send the summary to my email."
result = pipeline.process_query(query)

# Check results
if result.success:
    print(f"Functions to execute: {len(result.execution_plan.function_calls)}")
    for call in result.execution_plan.function_calls:
        print(f"- {call.function_name}({call.parameters})")
        print(f"  Reasoning: {call.reasoning}")
```

## Key Features

### 🤖 Multiple AI Model Support
- **Mistral 7B** (default): Excellent instruction following
- **Llama 2 7B**: Strong reasoning capabilities
- **Phi-3**: Efficient and fast
- **Gemma 7B**: Google's latest model
- **Custom models**: Easy to integrate your own

### 📚 Comprehensive Function Library (~50 functions)
- **Data Processing**: filter, sort, aggregate, group, merge data
- **File Operations**: read, write, copy, delete files
- **Communication**: email, SMS, API calls, Slack integration
- **Database**: query, insert, update, backup operations
- **Web Scraping**: extract data, download files, monitor changes
- **Analytics**: statistics, reports, visualizations, predictions
- **Automation**: scheduling, notifications, workflows

### 🧠 Intelligent Query Processing
- **Natural Language Understanding**: Extracts intent, entities, keywords
- **Dependency Management**: Automatically sequences function calls
- **Validation**: Ensures execution plans are safe and logical
- **Confidence Scoring**: Provides reliability metrics

### 🔒 Safety First
- **Execution disabled by default**: Functions are planned but not executed
- **Mock mode**: Safe testing with simulated results
- **Validation**: Comprehensive checks before execution
- **Audit logging**: Track all operations

## Documentation

- **[User Guide](docs/USER_GUIDE.md)**: Comprehensive usage guide
- **[API Reference](docs/API_REFERENCE.md)**: Detailed API documentation
- **[Examples](examples/)**: Working code examples
- **[Configuration](config/)**: Configuration options

## Testing

```bash
# Run tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html

# Run specific test
python -m pytest tests/test_pipeline.py::TestPipeline::test_pipeline_creation -v
```

## License

MIT License
