import logging
from typing import Dict, Any, List, Union


class MathCalculator:

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.calculation_history = []

    def add(self, numbers: List[Union[int, float]]) -> Dict[str, Any]:
        try:
            if len(numbers) < 2:
                return {
                    "success": False,
                    "error": "Need at least 2 numbers to add",
                    "result": None
                }

            result = sum(numbers)
            calculation = f"{' + '.join(map(str, numbers))} = {result}"

            self.calculation_history.append({
                "operation": "addition",
                "numbers": numbers,
                "result": result,
                "calculation": calculation
            })

            self.logger.info(f"Addition: {calculation}")

            return {
                "success": True,
                "result": result,
                "calculation": calculation,
                "operation": "addition",
                "numbers": numbers
            }

        except Exception as e:
            self.logger.error(f"Addition failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def subtract(self, numbers: List[Union[int, float]]) -> Dict[str, Any]:
        """Subtract numbers (first - second - third - ...)."""
        try:
            if len(numbers) < 2:
                return {
                    "success": False,
                    "error": "Need at least 2 numbers to subtract",
                    "result": None
                }
            
            result = numbers[0]
            for num in numbers[1:]:
                result -= num
            
            calculation = f"{' - '.join(map(str, numbers))} = {result}"
            
            # Store in history
            self.calculation_history.append({
                "operation": "subtraction",
                "numbers": numbers,
                "result": result,
                "calculation": calculation
            })
            
            self.logger.info(f"Subtraction: {calculation}")
            
            return {
                "success": True,
                "result": result,
                "calculation": calculation,
                "operation": "subtraction",
                "numbers": numbers
            }
            
        except Exception as e:
            self.logger.error(f"Subtraction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def multiply(self, numbers: List[Union[int, float]]) -> Dict[str, Any]:
        """Multiply multiple numbers."""
        try:
            if len(numbers) < 2:
                return {
                    "success": False,
                    "error": "Need at least 2 numbers to multiply",
                    "result": None
                }
            
            result = 1
            for num in numbers:
                result *= num
            
            calculation = f"{' × '.join(map(str, numbers))} = {result}"
            
            # Store in history
            self.calculation_history.append({
                "operation": "multiplication",
                "numbers": numbers,
                "result": result,
                "calculation": calculation
            })
            
            self.logger.info(f"Multiplication: {calculation}")
            
            return {
                "success": True,
                "result": result,
                "calculation": calculation,
                "operation": "multiplication",
                "numbers": numbers
            }
            
        except Exception as e:
            self.logger.error(f"Multiplication failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def divide(self, numbers: List[Union[int, float]]) -> Dict[str, Any]:
        """Divide numbers (first ÷ second ÷ third ÷ ...)."""
        try:
            if len(numbers) < 2:
                return {
                    "success": False,
                    "error": "Need at least 2 numbers to divide",
                    "result": None
                }
            
            # Check for division by zero
            if any(num == 0 for num in numbers[1:]):
                return {
                    "success": False,
                    "error": "Cannot divide by zero",
                    "result": None
                }
            
            result = numbers[0]
            for num in numbers[1:]:
                result /= num
            
            calculation = f"{' ÷ '.join(map(str, numbers))} = {result}"
            
            # Store in history
            self.calculation_history.append({
                "operation": "division",
                "numbers": numbers,
                "result": result,
                "calculation": calculation
            })
            
            self.logger.info(f"Division: {calculation}")
            
            return {
                "success": True,
                "result": result,
                "calculation": calculation,
                "operation": "division",
                "numbers": numbers
            }
            
        except Exception as e:
            self.logger.error(f"Division failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def get_history(self) -> List[Dict[str, Any]]:
        """Get calculation history."""
        return self.calculation_history.copy()
    
    def clear_history(self):
        """Clear calculation history."""
        self.calculation_history.clear()


# Default calculator instance
_default_calculator = MathCalculator()


def execute_add_numbers(parameters: Dict[str, Any]) -> Dict[str, Any]:
    numbers = parameters.get("numbers", [])
    result = _default_calculator.add(numbers)

    if result["success"]:
        print(f"CALCULATION:")
        print(f"   Operation: Addition")
        print(f"   Numbers: {' + '.join(map(str, numbers))}")
        print(f"   Result: {result['result']}")

    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result["result"]
    }


def execute_subtract_numbers(parameters: Dict[str, Any]) -> Dict[str, Any]:
    numbers = parameters.get("numbers", [])
    result = _default_calculator.subtract(numbers)

    if result["success"]:
        print(f"CALCULATION:")
        print(f"   Operation: Subtraction")
        print(f"   Numbers: {' - '.join(map(str, numbers))}")
        print(f"   Result: {result['result']}")

    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result["result"]
    }


def execute_multiply_numbers(parameters: Dict[str, Any]) -> Dict[str, Any]:
    numbers = parameters.get("numbers", [])
    result = _default_calculator.multiply(numbers)

    if result["success"]:
        print(f"CALCULATION:")
        print(f"   Operation: Multiplication")
        print(f"   Numbers: {' × '.join(map(str, numbers))}")
        print(f"   Result: {result['result']}")

    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result["result"]
    }


def execute_divide_numbers(parameters: Dict[str, Any]) -> Dict[str, Any]:
    numbers = parameters.get("numbers", [])
    result = _default_calculator.divide(numbers)

    if result["success"]:
        print(f"CALCULATION:")
        print(f"   Operation: Division")
        print(f"   Numbers: {' ÷ '.join(map(str, numbers))}")
        print(f"   Result: {result['result']}")

    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result["result"]
    }


def get_calculator() -> MathCalculator:
    return _default_calculator
