#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API clone {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<at::MemoryFormat>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clone";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "clone(Tensor self, *, MemoryFormat? memory_format=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, ::std::optional<at::MemoryFormat> memory_format);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<at::MemoryFormat> memory_format);
};

struct TORCH_API clone_out {
  using schema = at::Tensor & (const at::Tensor &, ::std::optional<at::MemoryFormat>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clone";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "clone.out(Tensor self, *, MemoryFormat? memory_format=None, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, ::std::optional<at::MemoryFormat> memory_format, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<at::MemoryFormat> memory_format, at::Tensor & out);
};

}} // namespace at::_ops
