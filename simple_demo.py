#!/usr/bin/env python3
"""
Simple Demo - Demonstrates the AI Function Call Pipeline without complex imports.
"""

import json
import sys
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any
from enum import Enum

# Add src to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


class FunctionCategory(Enum):
    """Categories for organizing functions."""
    DATA_PROCESSING = "data_processing"
    FILE_OPERATIONS = "file_operations"
    COMMUNICATION = "communication"
    DATABASE = "database"
    WEB_SCRAPING = "web_scraping"
    ANALYTICS = "analytics"


@dataclass
class FunctionCall:
    """Represents a function call."""
    function_name: str
    parameters: Dict[str, Any]
    confidence: float
    reasoning: str = ""


@dataclass
class ExecutionPlan:
    """Represents a complete execution plan."""
    function_calls: List[FunctionCall]
    dependencies: Dict[str, List[str]]
    reasoning: str
    confidence: float


@dataclass
class PipelineResult:
    """Result from pipeline processing."""
    query: str
    execution_plan: ExecutionPlan
    success: bool
    error_message: str = None


class SimplePipeline:
    """Simple demonstration pipeline."""
    
    def __init__(self):
        self.functions = self._get_available_functions()
    
    def _get_available_functions(self):
        """Get list of available functions."""
        return {
            # Data Processing
            "filter_data": {"category": "data_processing", "description": "Filter data based on criteria"},
            "sort_data": {"category": "data_processing", "description": "Sort data by field"},
            "aggregate_data": {"category": "data_processing", "description": "Aggregate data using operation"},
            "group_data": {"category": "data_processing", "description": "Group data by field"},
            "merge_datasets": {"category": "data_processing", "description": "Merge two datasets"},
            
            # File Operations
            "read_file": {"category": "file_operations", "description": "Read content from file"},
            "write_file": {"category": "file_operations", "description": "Write content to file"},
            "copy_file": {"category": "file_operations", "description": "Copy file"},
            "delete_file": {"category": "file_operations", "description": "Delete file"},
            "list_files": {"category": "file_operations", "description": "List files in directory"},
            
            # Communication
            "send_email": {"category": "communication", "description": "Send email message"},
            "send_sms": {"category": "communication", "description": "Send SMS message"},
            "make_api_call": {"category": "communication", "description": "Make HTTP API call"},
            "post_to_slack": {"category": "communication", "description": "Post to Slack channel"},
            
            # Database
            "execute_query": {"category": "database", "description": "Execute SQL query"},
            "insert_record": {"category": "database", "description": "Insert database record"},
            "update_record": {"category": "database", "description": "Update database record"},
            "backup_database": {"category": "database", "description": "Create database backup"},
            
            # Web Scraping
            "scrape_webpage": {"category": "web_scraping", "description": "Extract data from webpage"},
            "download_file": {"category": "web_scraping", "description": "Download file from URL"},
            "monitor_webpage": {"category": "web_scraping", "description": "Monitor webpage for changes"},
            
            # Analytics
            "calculate_statistics": {"category": "analytics", "description": "Calculate statistical measures"},
            "generate_report": {"category": "analytics", "description": "Generate analytical report"},
            "create_visualization": {"category": "analytics", "description": "Create data visualization"},
            "predict_values": {"category": "analytics", "description": "Make predictions using ML model"},
        }
    
    def process_query(self, query: str) -> PipelineResult:
        """Process a natural language query."""
        try:
            # Simple heuristic-based function selection
            function_calls = self._extract_functions_heuristic(query)
            dependencies = self._build_dependencies(function_calls)
            
            execution_plan = ExecutionPlan(
                function_calls=function_calls,
                dependencies=dependencies,
                reasoning=f"Heuristic-based plan for query: {query}",
                confidence=0.8
            )
            
            return PipelineResult(
                query=query,
                execution_plan=execution_plan,
                success=True
            )
            
        except Exception as e:
            return PipelineResult(
                query=query,
                execution_plan=ExecutionPlan([], {}, "", 0.0),
                success=False,
                error_message=str(e)
            )
    
    def _extract_functions_heuristic(self, query: str) -> List[FunctionCall]:
        """Extract functions using simple heuristics."""
        query_lower = query.lower()
        function_calls = []
        
        # Data retrieval patterns
        if any(word in query_lower for word in ["get", "retrieve", "fetch", "find", "show", "list"]):
            if any(word in query_lower for word in ["invoice", "data", "record", "customer", "user"]):
                function_calls.append(FunctionCall(
                    function_name="execute_query",
                    parameters={"query": "SELECT * FROM table WHERE condition", "database": "main_db"},
                    confidence=0.9,
                    reasoning="Retrieve data based on query keywords"
                ))
        
        # Data processing patterns
        if any(word in query_lower for word in ["summarize", "total", "sum", "calculate", "aggregate"]):
            function_calls.append(FunctionCall(
                function_name="aggregate_data",
                parameters={"data": "$execute_query", "operation": "sum", "field": "amount"},
                confidence=0.85,
                reasoning="Calculate totals or summaries"
            ))
        
        if any(word in query_lower for word in ["filter", "where", "only", "active"]):
            function_calls.append(FunctionCall(
                function_name="filter_data",
                parameters={"data": "$execute_query", "criteria": {"status": "active"}},
                confidence=0.8,
                reasoning="Filter data based on criteria"
            ))
        
        # Communication patterns - Email
        if any(word in query_lower for word in ["email", "mail"]) and "send" in query_lower:
            function_calls.append(FunctionCall(
                function_name="send_email",
                parameters={
                    "to": "<EMAIL>",
                    "subject": "Report Summary",
                    "body": "$aggregate_data"
                },
                confidence=0.75,
                reasoning="Send results via email"
            ))

        # Communication patterns - SMS
        if any(word in query_lower for word in ["sms", "text", "message"]) and "send" in query_lower:
            # Extract phone number from query
            import re
            phone_pattern = r'\b\d{10,15}\b'  # Match 10-15 digit phone numbers
            phone_matches = re.findall(phone_pattern, query)
            phone_number = phone_matches[0] if phone_matches else "1234567890"

            # Extract message content
            message_content = "good morning"  # default
            if "with message" in query_lower:
                # Extract text after "with message"
                message_start = query_lower.find("with message") + len("with message")
                message_content = query[message_start:].strip()
            elif "message" in query_lower:
                # Extract text after "message"
                words = query_lower.split()
                if "message" in words:
                    message_idx = words.index("message")
                    if message_idx + 1 < len(words):
                        message_content = " ".join(query.split()[message_idx + 1:])

            function_calls.append(FunctionCall(
                function_name="send_sms",
                parameters={
                    "to": phone_number,
                    "message": message_content
                },
                confidence=0.85,
                reasoning=f"Send SMS to {phone_number} with message: {message_content}"
            ))
        
        # Reporting patterns
        if any(word in query_lower for word in ["report", "generate", "create", "analysis"]):
            function_calls.append(FunctionCall(
                function_name="generate_report",
                parameters={"data": "$aggregate_data", "template": "summary_report", "format": "pdf"},
                confidence=0.8,
                reasoning="Generate comprehensive report"
            ))
        
        # Visualization patterns
        if any(word in query_lower for word in ["chart", "graph", "visualization", "plot"]):
            function_calls.append(FunctionCall(
                function_name="create_visualization",
                parameters={"data": "$aggregate_data", "chart_type": "bar", "title": "Data Summary"},
                confidence=0.75,
                reasoning="Create visual representation"
            ))
        
        # Web scraping patterns
        if any(word in query_lower for word in ["scrape", "monitor", "website", "competitor"]):
            function_calls.append(FunctionCall(
                function_name="scrape_webpage",
                parameters={"url": "https://example.com", "selectors": {"price": ".price"}},
                confidence=0.7,
                reasoning="Extract data from website"
            ))
        
        # Backup patterns
        if any(word in query_lower for word in ["backup", "save", "archive"]):
            function_calls.append(FunctionCall(
                function_name="backup_database",
                parameters={"database": "main_db", "backup_path": "/backups/"},
                confidence=0.8,
                reasoning="Create data backup"
            ))
        
        return function_calls
    
    def _build_dependencies(self, function_calls: List[FunctionCall]) -> Dict[str, List[str]]:
        """Build dependencies between functions."""
        dependencies = {}
        
        # Simple dependency rules
        for call in function_calls:
            deps = []
            
            # Functions that depend on data retrieval
            if call.function_name in ["aggregate_data", "filter_data", "generate_report"]:
                if any(fc.function_name == "execute_query" for fc in function_calls):
                    deps.append("execute_query")
            
            # Functions that depend on data processing
            if call.function_name in ["send_email", "create_visualization"]:
                if any(fc.function_name == "aggregate_data" for fc in function_calls):
                    deps.append("aggregate_data")
                elif any(fc.function_name == "filter_data" for fc in function_calls):
                    deps.append("filter_data")
            
            # Functions that depend on web scraping
            if call.function_name in ["update_record"]:
                if any(fc.function_name == "scrape_webpage" for fc in function_calls):
                    deps.append("scrape_webpage")
            
            if deps:
                dependencies[call.function_name] = deps
        
        return dependencies
    
    def get_available_functions(self) -> List[str]:
        """Get list of available function names."""
        return list(self.functions.keys())
    
    def search_functions(self, term: str) -> List[str]:
        """Search functions by keyword."""
        term_lower = term.lower()
        results = []

        for func_name, func_info in self.functions.items():
            if (term_lower in func_name.lower() or
                term_lower in func_info["description"].lower() or
                term_lower in func_info["category"]):
                results.append(func_name)

        return results

    def execute_function(self, function_call: FunctionCall) -> Dict[str, Any]:
        """Execute a single function call."""
        try:
            if function_call.function_name == "send_sms":
                # Import and use the SMS implementation
                from functions.sms_implementation import execute_send_sms
                return execute_send_sms(function_call.parameters)

            elif function_call.function_name == "send_email":
                # Mock email implementation
                return {
                    "success": True,
                    "result": f"Email sent to {function_call.parameters.get('to', 'unknown')}",
                    "error": None
                }

            else:
                # Mock implementation for other functions
                return {
                    "success": True,
                    "result": f"Function {function_call.function_name} executed with parameters: {function_call.parameters}",
                    "error": None
                }

        except Exception as e:
            return {
                "success": False,
                "result": None,
                "error": str(e)
            }


def main():
    """Run the simple demonstration."""
    print("🚀 AI Function Call Pipeline - Simple Demo")
    print("=" * 50)
    
    # Create pipeline
    pipeline = SimplePipeline()
    
    print(f"📚 Available Functions: {len(pipeline.get_available_functions())}")
    
    # Example queries
    example_queries = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a report.",
        "Search for transactions over $1000, analyze the patterns, and create a visualization.",
        "Scrape competitor prices, update our database, and send alerts.",
        "Backup the database and notify the admin team."
    ]
    
    print("\n🔍 Processing Example Queries:")
    print("-" * 40)
    
    for i, query in enumerate(example_queries, 1):
        print(f"\n{i}. Query: {query}")
        
        result = pipeline.process_query(query)
        
        if result.success:
            plan = result.execution_plan
            print(f"✅ Success! (Confidence: {plan.confidence:.2f})")
            print(f"🔧 Functions: {len(plan.function_calls)}")
            
            if plan.function_calls:
                print("\n📋 Execution Plan:")
                for j, call in enumerate(plan.function_calls, 1):
                    print(f"   {j}. {call.function_name}")
                    print(f"      Parameters: {call.parameters}")
                    print(f"      Reasoning: {call.reasoning}")
                
                if plan.dependencies:
                    print("\n🔗 Dependencies:")
                    for func, deps in plan.dependencies.items():
                        if deps:
                            print(f"   {func} ← {', '.join(deps)}")
        else:
            print(f"❌ Failed: {result.error_message}")
        
        print("-" * 40)
    
    # Function search demo
    print("\n🔍 Function Search Demo:")
    search_terms = ["email", "data", "report", "backup"]
    
    for term in search_terms:
        results = pipeline.search_functions(term)
        print(f"'{term}': {results}")
    
    print("\n✨ Simple Demo Complete!")
    print("This demonstrates the core concept of converting natural language")
    print("queries into structured function call sequences with dependencies.")


if __name__ == "__main__":
    main()
