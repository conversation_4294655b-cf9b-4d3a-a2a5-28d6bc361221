from sympy.printing.tree import tree
from sympy.testing.pytest import XFAIL


# Remove this flag after making _assumptions cache deterministic.
@XFAIL
def test_print_tree_MatAdd():
    from sympy.matrices.expressions import MatrixSymbol
    A = MatrixSymbol('A', 3, 3)
    B = MatrixSymbol('B', 3, 3)

    test_str = [
        'MatAdd: A + B\n',
        'algebraic: False\n',
        'commutative: False\n',
        'complex: False\n',
        'composite: False\n',
        'even: False\n',
        'extended_negative: False\n',
        'extended_nonnegative: False\n',
        'extended_nonpositive: False\n',
        'extended_nonzero: False\n',
        'extended_positive: False\n',
        'extended_real: False\n',
        'imaginary: False\n',
        'integer: False\n',
        'irrational: False\n',
        'negative: False\n',
        'noninteger: False\n',
        'nonnegative: False\n',
        'nonpositive: False\n',
        'nonzero: False\n',
        'odd: False\n',
        'positive: False\n',
        'prime: False\n',
        'rational: False\n',
        'real: False\n',
        'transcendental: False\n',
        'zero: False\n',
        '+-MatrixSymbol: A\n',
        '| algebraic: False\n',
        '| commutative: False\n',
        '| complex: False\n',
        '| composite: False\n',
        '| even: False\n',
        '| extended_negative: False\n',
        '| extended_nonnegative: False\n',
        '| extended_nonpositive: False\n',
        '| extended_nonzero: False\n',
        '| extended_positive: False\n',
        '| extended_real: False\n',
        '| imaginary: False\n',
        '| integer: False\n',
        '| irrational: False\n',
        '| negative: False\n',
        '| noninteger: False\n',
        '| nonnegative: False\n',
        '| nonpositive: False\n',
        '| nonzero: False\n',
        '| odd: False\n',
        '| positive: False\n',
        '| prime: False\n',
        '| rational: False\n',
        '| real: False\n',
        '| transcendental: False\n',
        '| zero: False\n',
        '| +-Symbol: A\n',
        '| | commutative: True\n',
        '| +-Integer: 3\n',
        '| | algebraic: True\n',
        '| | commutative: True\n',
        '| | complex: True\n',
        '| | extended_negative: False\n',
        '| | extended_nonnegative: True\n',
        '| | extended_real: True\n',
        '| | finite: True\n',
        '| | hermitian: True\n',
        '| | imaginary: False\n',
        '| | infinite: False\n',
        '| | integer: True\n',
        '| | irrational: False\n',
        '| | negative: False\n',
        '| | noninteger: False\n',
        '| | nonnegative: True\n',
        '| | rational: True\n',
        '| | real: True\n',
        '| | transcendental: False\n',
        '| +-Integer: 3\n',
        '|   algebraic: True\n',
        '|   commutative: True\n',
        '|   complex: True\n',
        '|   extended_negative: False\n',
        '|   extended_nonnegative: True\n',
        '|   extended_real: True\n',
        '|   finite: True\n',
        '|   hermitian: True\n',
        '|   imaginary: False\n',
        '|   infinite: False\n',
        '|   integer: True\n',
        '|   irrational: False\n',
        '|   negative: False\n',
        '|   noninteger: False\n',
        '|   nonnegative: True\n',
        '|   rational: True\n',
        '|   real: True\n',
        '|   transcendental: False\n',
        '+-MatrixSymbol: B\n',
        '  algebraic: False\n',
        '  commutative: False\n',
        '  complex: False\n',
        '  composite: False\n',
        '  even: False\n',
        '  extended_negative: False\n',
        '  extended_nonnegative: False\n',
        '  extended_nonpositive: False\n',
        '  extended_nonzero: False\n',
        '  extended_positive: False\n',
        '  extended_real: False\n',
        '  imaginary: False\n',
        '  integer: False\n',
        '  irrational: False\n',
        '  negative: False\n',
        '  noninteger: False\n',
        '  nonnegative: False\n',
        '  nonpositive: False\n',
        '  nonzero: False\n',
        '  odd: False\n',
        '  positive: False\n',
        '  prime: False\n',
        '  rational: False\n',
        '  real: False\n',
        '  transcendental: False\n',
        '  zero: False\n',
        '  +-Symbol: B\n',
        '  | commutative: True\n',
        '  +-Integer: 3\n',
        '  | algebraic: True\n',
        '  | commutative: True\n',
        '  | complex: True\n',
        '  | extended_negative: False\n',
        '  | extended_nonnegative: True\n',
        '  | extended_real: True\n',
        '  | finite: True\n',
        '  | hermitian: True\n',
        '  | imaginary: False\n',
        '  | infinite: False\n',
        '  | integer: True\n',
        '  | irrational: False\n',
        '  | negative: False\n',
        '  | noninteger: False\n',
        '  | nonnegative: True\n',
        '  | rational: True\n',
        '  | real: True\n',
        '  | transcendental: False\n',
        '  +-Integer: 3\n',
        '    algebraic: True\n',
        '    commutative: True\n',
        '    complex: True\n',
        '    extended_negative: False\n',
        '    extended_nonnegative: True\n',
        '    extended_real: True\n',
        '    finite: True\n',
        '    hermitian: True\n',
        '    imaginary: False\n',
        '    infinite: False\n',
        '    integer: True\n',
        '    irrational: False\n',
        '    negative: False\n',
        '    noninteger: False\n',
        '    nonnegative: True\n',
        '    rational: True\n',
        '    real: True\n',
        '    transcendental: False\n'
    ]

    assert tree(A + B) == "".join(test_str)


def test_print_tree_MatAdd_noassumptions():
    from sympy.matrices.expressions import MatrixSymbol
    A = MatrixSymbol('A', 3, 3)
    B = MatrixSymbol('B', 3, 3)

    test_str = \
"""MatAdd: A + B
+-MatrixSymbol: A
| +-Str: A
| +-Integer: 3
| +-Integer: 3
+-MatrixSymbol: B
  +-Str: B
  +-Integer: 3
  +-Integer: 3
"""

    assert tree(A + B, assumptions=False) == test_str
