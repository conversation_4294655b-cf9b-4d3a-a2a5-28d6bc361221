#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API bernoulli {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<at::Generator>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "bernoulli(Tensor self, *, Generator? generator=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, ::std::optional<at::Generator> generator);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<at::Generator> generator);
};

struct TORCH_API bernoulli_out {
  using schema = at::Tensor & (const at::Tensor &, ::std::optional<at::Generator>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "bernoulli.out(Tensor self, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, ::std::optional<at::Generator> generator, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<at::Generator> generator, at::Tensor & out);
};

struct TORCH_API bernoulli__Tensor {
  using schema = at::Tensor & (at::Tensor &, const at::Tensor &, ::std::optional<at::Generator>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli_";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "bernoulli_.Tensor(Tensor(a!) self, Tensor p, *, Generator? generator=None) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator);
};

struct TORCH_API bernoulli__float {
  using schema = at::Tensor & (at::Tensor &, double, ::std::optional<at::Generator>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli_";
  static constexpr const char* overload_name = "float";
  static constexpr const char* schema_str = "bernoulli_.float(Tensor(a!) self, float p=0.5, *, Generator? generator=None) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, double p, ::std::optional<at::Generator> generator);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, double p, ::std::optional<at::Generator> generator);
};

struct TORCH_API bernoulli_p {
  using schema = at::Tensor (const at::Tensor &, double, ::std::optional<at::Generator>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "p";
  static constexpr const char* schema_str = "bernoulli.p(Tensor self, float p, *, Generator? generator=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, double p, ::std::optional<at::Generator> generator);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double p, ::std::optional<at::Generator> generator);
};

struct TORCH_API bernoulli_Tensor_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, ::std::optional<at::Generator>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "Tensor_out";
  static constexpr const char* schema_str = "bernoulli.Tensor_out(Tensor self, Tensor p, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator, at::Tensor & out);
};

struct TORCH_API bernoulli_Tensor {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, ::std::optional<at::Generator>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "bernoulli.Tensor(Tensor self, Tensor p, *, Generator? generator=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & p, ::std::optional<at::Generator> generator);
};

struct TORCH_API bernoulli_float_out {
  using schema = at::Tensor & (const at::Tensor &, double, ::std::optional<at::Generator>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::bernoulli";
  static constexpr const char* overload_name = "float_out";
  static constexpr const char* schema_str = "bernoulli.float_out(Tensor self, float p=0.5, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, double p, ::std::optional<at::Generator> generator, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double p, ::std::optional<at::Generator> generator, at::Tensor & out);
};

}} // namespace at::_ops
