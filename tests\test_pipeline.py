"""
Tests for the AI Function Call Pipeline.
"""

import pytest
import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pipeline.core import Fun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ineBuilder
from models.model_interface import <PERSON><PERSON>onfig, ExecutionPlan, FunctionCall
from functions.function_registry import FunctionRegistry
from parsers.query_parser import QueryParser
from utils.execution_engine import MockExecutionEngine


class TestFunctionRegistry:
    """Test the function registry."""
    
    def test_registry_initialization(self):
        """Test that registry initializes with functions."""
        registry = FunctionRegistry()
        functions = registry.get_all_functions()
        
        assert len(functions) > 0
        assert any(func.name == "filter_data" for func in functions)
        assert any(func.name == "send_email" for func in functions)
    
    def test_function_search(self):
        """Test function search functionality."""
        registry = FunctionRegistry()
        
        # Search for email functions
        email_functions = registry.search_functions("email")
        assert len(email_functions) > 0
        assert any("email" in func.name.lower() for func in email_functions)
        
        # Search for data functions
        data_functions = registry.search_functions("data")
        assert len(data_functions) > 0
    
    def test_get_function_by_name(self):
        """Test getting specific function by name."""
        registry = FunctionRegistry()
        
        func = registry.get_function("filter_data")
        assert func is not None
        assert func.name == "filter_data"
        assert len(func.parameters) > 0
        
        # Test non-existent function
        func = registry.get_function("non_existent_function")
        assert func is None


class TestQueryParser:
    """Test the query parser."""
    
    def test_basic_parsing(self):
        """Test basic query parsing."""
        parser = QueryParser()
        
        query = "Get all invoices for March and send summary to my email"
        parsed = parser.parse(query)
        
        assert parsed.original_query == query
        assert len(parsed.intents) > 0
        assert len(parsed.keywords) > 0
        assert parsed.confidence > 0
    
    def test_entity_extraction(self):
        """Test entity extraction."""
        parser = QueryParser()
        
        query = "Send <NAME_EMAIL> with March data"
        parsed = parser.parse(query)
        
        # Should extract email
        assert len(parsed.entities["emails"]) > 0
        assert "<EMAIL>" in parsed.entities["emails"]
        
        # Should extract date reference
        assert any("march" in date.lower() for date in parsed.entities["dates"])
    
    def test_temporal_expressions(self):
        """Test temporal expression extraction."""
        parser = QueryParser()
        
        query = "Schedule daily report for next Monday"
        parsed = parser.parse(query)
        
        assert len(parsed.temporal_expressions) > 0


class TestExecutionEngine:
    """Test the execution engine."""
    
    def test_mock_execution(self):
        """Test mock execution engine."""
        engine = MockExecutionEngine()
        
        # Create a simple execution plan
        function_calls = [
            FunctionCall(
                function_name="filter_data",
                parameters={"data": [], "criteria": {}},
                confidence=0.8
            ),
            FunctionCall(
                function_name="send_email",
                parameters={"to": "<EMAIL>", "subject": "Test", "body": "Test"},
                confidence=0.7
            )
        ]
        
        execution_plan = ExecutionPlan(
            function_calls=function_calls,
            dependencies={"send_email": ["filter_data"]},
            reasoning="Test execution plan",
            confidence=0.75
        )
        
        # Execute the plan
        result = engine.execute_plan(execution_plan)
        
        assert result.success
        assert len(result.results) == 2
        assert all(res.success for res in result.results)
    
    def test_dependency_ordering(self):
        """Test that dependencies are respected in execution order."""
        engine = MockExecutionEngine()
        
        # Create plan with dependencies
        function_calls = [
            FunctionCall("step_3", {}, 0.8),
            FunctionCall("step_1", {}, 0.8),
            FunctionCall("step_2", {}, 0.8),
        ]
        
        execution_plan = ExecutionPlan(
            function_calls=function_calls,
            dependencies={
                "step_2": ["step_1"],
                "step_3": ["step_2"]
            },
            reasoning="Test dependency ordering",
            confidence=0.8
        )
        
        result = engine.execute_plan(execution_plan)
        
        # Check that execution order respects dependencies
        execution_order = [res.function_name for res in result.results]
        assert execution_order.index("step_1") < execution_order.index("step_2")
        assert execution_order.index("step_2") < execution_order.index("step_3")


class TestPipeline:
    """Test the main pipeline."""
    
    def test_pipeline_creation(self):
        """Test pipeline creation."""
        # Create a minimal pipeline for testing
        registry = FunctionRegistry()
        
        # Mock pipeline since we might not have models available
        class MockPipeline:
            def __init__(self):
                self.function_registry = registry
            
            def get_available_functions(self):
                return [func.name for func in self.function_registry.get_all_functions()]
            
            def search_functions(self, term):
                results = self.function_registry.search_functions(term)
                return [func.name for func in results]
        
        pipeline = MockPipeline()
        
        functions = pipeline.get_available_functions()
        assert len(functions) > 0
        
        email_functions = pipeline.search_functions("email")
        assert len(email_functions) > 0
    
    def test_pipeline_validation(self):
        """Test pipeline validation logic."""
        from pipeline.core import FunctionCallPipeline
        
        # Create a mock pipeline with validation
        class TestPipeline(FunctionCallPipeline):
            def __init__(self):
                self.function_registry = FunctionRegistry()
            
            def _validate_execution_plan(self, execution_plan):
                return super()._validate_execution_plan(execution_plan)
            
            def _has_dependency_cycle(self, dependencies):
                return super()._has_dependency_cycle(dependencies)
        
        pipeline = TestPipeline()
        
        # Test valid plan
        valid_plan = ExecutionPlan(
            function_calls=[
                FunctionCall("filter_data", {"data": [], "criteria": {}}, 0.8),
                FunctionCall("send_email", {"to": "<EMAIL>", "subject": "Test", "body": "Test"}, 0.7)
            ],
            dependencies={"send_email": ["filter_data"]},
            reasoning="Valid test plan",
            confidence=0.75
        )
        
        validation = pipeline._validate_execution_plan(valid_plan)
        assert validation["valid"]
        
        # Test circular dependency
        circular_deps = {
            "func_a": ["func_b"],
            "func_b": ["func_a"]
        }
        
        assert pipeline._has_dependency_cycle(circular_deps)
        
        # Test no circular dependency
        valid_deps = {
            "func_b": ["func_a"],
            "func_c": ["func_b"]
        }
        
        assert not pipeline._has_dependency_cycle(valid_deps)


class TestIntegration:
    """Integration tests."""
    
    def test_end_to_end_demo(self):
        """Test end-to-end pipeline with demo data."""
        # Create demo pipeline
        from examples.basic_example import create_demo_pipeline
        
        pipeline = create_demo_pipeline()
        
        # Test with sample query
        query = "Get all invoices for March and send summary to my email"
        result = pipeline.process_query(query)
        
        assert result.success
        assert len(result.execution_plan.function_calls) > 0
        
        # Check that reasonable functions were selected
        function_names = [call.function_name for call in result.execution_plan.function_calls]
        assert any("query" in name.lower() or "get" in name.lower() for name in function_names)
        assert any("email" in name.lower() or "send" in name.lower() for name in function_names)


# Test fixtures
@pytest.fixture
def sample_registry():
    """Create a sample function registry for testing."""
    return FunctionRegistry()


@pytest.fixture
def sample_parser():
    """Create a sample query parser for testing."""
    return QueryParser()


@pytest.fixture
def sample_execution_engine():
    """Create a sample execution engine for testing."""
    return MockExecutionEngine()


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
