# Core dependencies
torch>=2.0.0
transformers>=4.30.0
tokenizers>=0.13.0
accelerate>=0.20.0

# Optional GPU support
# torch-audio  # Uncomment if needed
# torchaudio   # Uncomment if needed

# Data processing
numpy>=1.21.0
pandas>=1.5.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=1.0.0
click>=8.0.0

# Logging and monitoring
loguru>=0.7.0

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Development tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Optional: For advanced NLP features
# spacy>=3.6.0
# nltk>=3.8.0

# Optional: For API integrations
# requests>=2.28.0
# httpx>=0.24.0

# Optional: For database support
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0  # PostgreSQL
# pymongo>=4.0.0          # MongoDB

# Optional: For web scraping
# beautifulsoup4>=4.12.0
# selenium>=4.10.0

# Optional: For visualization
# matplotlib>=3.7.0
# plotly>=5.15.0

# Optional: For scheduling
# apscheduler>=3.10.0

# Optional: For email
# smtplib (built-in)
# email-validator>=2.0.0
