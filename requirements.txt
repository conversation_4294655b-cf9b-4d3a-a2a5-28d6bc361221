# Core dependencies for Mistral AI and other models
torch>=2.0.0
transformers>=4.30.0
tokenizers>=0.13.0
accelerate>=0.20.0

# Mistral AI specific dependencies
mistral-inference>=0.0.7
mistralai>=0.1.0

# Additional model support
sentencepiece>=0.1.99  # For Mistral tokenization
protobuf>=3.20.0

# Optional GPU support
# torch-audio  # Uncomment if needed
# torchaudio   # Uncomment if needed

# Data processing
numpy>=1.21.0
pandas>=1.5.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=1.0.0
click>=8.0.0

# Logging and monitoring
loguru>=0.7.0

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Development tools (optional)
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0

# Optional: For advanced NLP features
# spacy>=3.6.0
# nltk>=3.8.0

# API integrations (needed for SMS and other communications)
requests>=2.28.0
httpx>=0.24.0

# Optional: For database support
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0  # PostgreSQL
# pymongo>=4.0.0          # MongoDB

# Optional: For web scraping
# beautifulsoup4>=4.12.0
# selenium>=4.10.0

# Optional: For visualization
# matplotlib>=3.7.0
# plotly>=5.15.0

# Optional: For scheduling
# apscheduler>=3.10.0

# Optional: For email
# smtplib (built-in)
# email-validator>=2.0.0
