# Deprecated function signatures. These are exposed in Python, but not included
# in the error message suggestions.

- name: add(<PERSON>sor self, <PERSON>ala<PERSON> alpha, <PERSON>sor other) -> Tensor
  aten: add(self, other, alpha)

- name: add_(Tensor(a!) self, <PERSON><PERSON><PERSON> alpha, Tensor other) -> Tensor(a!)
  aten: add_(self, other, alpha)

- name: add(Tensor self, <PERSON>alar alpha, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
  aten: add_out(out, self, other, alpha)

- name: addbmm(Scalar beta, Tensor self, Scalar alpha, Tensor batch1, Tensor batch2) -> Tensor
  aten: addbmm(self, batch1, batch2, beta, alpha)

- name: addbmm_(Scalar beta, Tensor(a!) self, Scalar alpha, Tensor batch1, Tensor batch2) -> Tensor(a!)
  aten: addbmm_(self, batch1, batch2, beta, alpha)

- name: addbmm(Scalar beta, Tensor self, Scalar alpha, Tensor batch1, Tensor batch2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addbmm_out(out, self, batch1, batch2, beta, alpha)

- name: addbmm(<PERSON><PERSON><PERSON> beta, Tensor self, Tensor batch1, Tensor batch2) -> Tensor
  aten: addbmm(self, batch1, batch2, beta, 1)

- name: addbmm_(Scalar beta, Tensor(a!) self, Tensor batch1, Tensor batch2) -> Tensor(a!)
  aten: addbmm_(self, batch1, batch2, beta, 1)

- name: addbmm(Scalar beta, Tensor self, Tensor batch1, Tensor batch2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addbmm_out(out, self, batch1, batch2, beta, 1)

- name: addcdiv(Tensor self, Scalar value, Tensor tensor1, Tensor tensor2) -> Tensor
  aten: addcdiv(self, tensor1, tensor2, value)

- name: addcdiv_(Tensor(a!) self, Scalar value, Tensor tensor1, Tensor tensor2) -> Tensor(a!)
  aten: addcdiv_(self, tensor1, tensor2, value)

- name: addcdiv(Tensor self, Scalar value, Tensor tensor1, Tensor tensor2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addcdiv_out(out, self, tensor1, tensor2, value)

- name: addcmul(Tensor self, Scalar value, Tensor tensor1, Tensor tensor2) -> Tensor
  aten: addcmul(self, tensor1, tensor2, value)

- name: addcmul_(Tensor(a!) self, Scalar value, Tensor tensor1, Tensor tensor2) -> Tensor(a!)
  aten: addcmul_(self, tensor1, tensor2, value)

- name: addcmul(Tensor self, Scalar value, Tensor tensor1, Tensor tensor2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addcmul_out(out, self, tensor1, tensor2, value)

- name: addmm(Scalar beta, Tensor self, Scalar alpha, Tensor mat1, Tensor mat2) -> Tensor
  aten: addmm(self, mat1, mat2, beta, alpha)

- name: addmm_(Scalar beta, Tensor(a!) self, Scalar alpha, Tensor mat1, Tensor mat2) -> Tensor(a!)
  aten: addmm_(self, mat1, mat2, beta, alpha)

- name: addmm(Scalar beta, Tensor self, Scalar alpha, Tensor mat1, Tensor mat2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addmm_out(out, self, mat1, mat2, beta, alpha)

- name: addmm(Scalar beta, Tensor self, Tensor mat1, Tensor mat2) -> Tensor
  aten: addmm(self, mat1, mat2, beta, 1)

- name: addmm_(Scalar beta, Tensor(a!) self, Tensor mat1, Tensor mat2) -> Tensor(a!)
  aten: addmm_(self, mat1, mat2, beta, 1)

- name: addmm(Scalar beta, Tensor self, Tensor mat1, Tensor mat2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addmm_out(out, self, mat1, mat2, beta, 1)

- name: sspaddmm(Scalar beta, Tensor self, Scalar alpha, Tensor mat1, Tensor mat2) -> Tensor
  aten: sspaddmm(self, mat1, mat2, beta, alpha)

- name: sspaddmm(Scalar beta, Tensor self, Tensor mat1, Tensor mat2) -> Tensor
  aten: sspaddmm(self, mat1, mat2, beta, 1)

- name: addmv(Scalar beta, Tensor self, Scalar alpha, Tensor mat, Tensor vec) -> Tensor
  aten: addmv(self, mat, vec, beta, alpha)

- name: addmv_(Scalar beta, Tensor(a!) self, Scalar alpha, Tensor mat, Tensor vec) -> Tensor(a!)
  aten: addmv_(self, mat, vec, beta, alpha)

- name: addmv(Scalar beta, Tensor self, Scalar alpha, Tensor mat, Tensor vec, *, Tensor(a!) out) -> Tensor(a!)
  aten: addmv_out(out, self, mat, vec, beta, alpha)

- name: addmv(Scalar beta, Tensor self, Tensor mat, Tensor vec) -> Tensor
  aten: addmv(self, mat, vec, beta, 1)

- name: addmv_(Scalar beta, Tensor(a!) self, Tensor mat, Tensor vec) -> Tensor(a!)
  aten: addmv_(self, mat, vec, beta, 1)

- name: addmv(Scalar beta, Tensor self, Tensor mat, Tensor vec, *, Tensor(a!) out) -> Tensor(a!)
  aten: addmv_out(out, self, mat, vec, beta, 1)

- name: addr(Scalar beta, Tensor self, Scalar alpha, Tensor vec1, Tensor vec2) -> Tensor
  aten: addr(self, vec1, vec2, beta, alpha)

- name: addr_(Scalar beta, Tensor(a!) self, Scalar alpha, Tensor vec1, Tensor vec2) -> Tensor(a!)
  aten: addr_(self, vec1, vec2, beta, alpha)

- name: addr(Scalar beta, Tensor self, Scalar alpha, Tensor vec1, Tensor vec2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addr_out(out, self, vec1, vec2, beta, alpha)

- name: addr(Scalar beta, Tensor self, Tensor vec1, Tensor vec2) -> Tensor
  aten: addr(self, vec1, vec2, beta, 1)

- name: addr_(Scalar beta, Tensor(a!) self, Tensor vec1, Tensor vec2) -> Tensor(a!)
  aten: addr_(self, vec1, vec2, beta, 1)

- name: addr(Scalar beta, Tensor self, Tensor vec1, Tensor vec2, *, Tensor(a!) out) -> Tensor(a!)
  aten: addr_out(out, self, vec1, vec2, beta, 1)

- name: baddbmm(Scalar beta, Tensor self, Scalar alpha, Tensor batch1, Tensor batch2) -> Tensor
  aten: baddbmm(self, batch1, batch2, beta, alpha)

- name: baddbmm_(Scalar beta, Tensor(a!) self, Scalar alpha, Tensor batch1, Tensor batch2) -> Tensor(a!)
  aten: baddbmm_(self, batch1, batch2, beta, alpha)

- name: baddbmm(Scalar beta, Tensor self, Scalar alpha, Tensor batch1, Tensor batch2, *, Tensor(a!) out) -> Tensor(a!)
  aten: baddbmm_out(out, self, batch1, batch2, beta, alpha)

- name: baddbmm(Scalar beta, Tensor self, Tensor batch1, Tensor batch2) -> Tensor
  aten: baddbmm(self, batch1, batch2, beta, 1)

- name: baddbmm_(Scalar beta, Tensor(a!) self, Tensor batch1, Tensor batch2) -> Tensor(a!)
  aten: baddbmm_(self, batch1, batch2, beta, 1)

- name: baddbmm(Scalar beta, Tensor self, Tensor batch1, Tensor batch2, *, Tensor(a!) out) -> Tensor(a!)
  aten: baddbmm_out(out, self, batch1, batch2, beta, 1)

- name: sub(Tensor self, Scalar alpha, Tensor other) -> Tensor
  aten: sub(self, other, alpha)

- name: sub_(Tensor(a!) self, Scalar alpha, Tensor other) -> Tensor(a!)
  aten: sub_(self, other, alpha)

- name: sub(Tensor self, Scalar alpha, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
  aten: sub_out(out, self, other, alpha)
