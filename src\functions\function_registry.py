"""
Function Registry - Central registry for all available functions in the pipeline.
"""

from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass
from enum import Enum


class FunctionCategory(Enum):
    """Categories for organizing functions."""
    DATA_PROCESSING = "data_processing"
    FILE_OPERATIONS = "file_operations"
    COMMUNICATION = "communication"
    DATABASE = "database"
    WEB_SCRAPING = "web_scraping"
    ANALYTICS = "analytics"
    AUTHENTICATION = "authentication"
    SCHEDULING = "scheduling"
    NOTIFICATION = "notification"
    CONVERSION = "conversion"
    VALIDATION = "validation"
    SEARCH = "search"


@dataclass
class FunctionParameter:
    """Represents a function parameter."""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None


@dataclass
class FunctionDefinition:
    """Represents a function definition in the registry."""
    name: str
    description: str
    category: FunctionCategory
    parameters: List[FunctionParameter]
    returns: str
    examples: List[str]
    implementation: Optional[Callable] = None
    dependencies: List[str] = None


class FunctionRegistry:
    """Central registry for all available functions."""
    
    def __init__(self):
        self.functions: Dict[str, FunctionDefinition] = {}
        self._register_all_functions()
    
    def register_function(self, func_def: FunctionDefinition):
        """Register a function in the registry."""
        self.functions[func_def.name] = func_def
    
    def get_function(self, name: str) -> Optional[FunctionDefinition]:
        """Get a function definition by name."""
        return self.functions.get(name)
    
    def get_functions_by_category(self, category: FunctionCategory) -> List[FunctionDefinition]:
        """Get all functions in a specific category."""
        return [func for func in self.functions.values() if func.category == category]
    
    def search_functions(self, query: str) -> List[FunctionDefinition]:
        """Search functions by name or description."""
        query_lower = query.lower()
        results = []
        for func in self.functions.values():
            if (query_lower in func.name.lower() or 
                query_lower in func.description.lower()):
                results.append(func)
        return results
    
    def get_all_functions(self) -> List[FunctionDefinition]:
        """Get all registered functions."""
        return list(self.functions.values())
    
    def _register_all_functions(self):
        """Register all predefined functions."""
        # Data Processing Functions
        self._register_data_processing_functions()
        # File Operations Functions
        self._register_file_operations_functions()
        # Communication Functions
        self._register_communication_functions()
        # Database Functions
        self._register_database_functions()
        # Web Scraping Functions
        self._register_web_scraping_functions()
        # Analytics Functions
        self._register_analytics_functions()
        # Authentication Functions
        self._register_authentication_functions()
        # Scheduling Functions
        self._register_scheduling_functions()
        # Notification Functions
        self._register_notification_functions()
        # Conversion Functions
        self._register_conversion_functions()
        # Validation Functions
        self._register_validation_functions()
        # Search Functions
        self._register_search_functions()

    def _register_data_processing_functions(self):
        """Register data processing functions."""
        functions = [
            FunctionDefinition(
                name="filter_data",
                description="Filter data based on specified criteria",
                category=FunctionCategory.DATA_PROCESSING,
                parameters=[
                    FunctionParameter("data", "list", "Input data to filter"),
                    FunctionParameter("criteria", "dict", "Filter criteria"),
                ],
                returns="list",
                examples=["Filter invoices by date range", "Filter users by status"]
            ),
            FunctionDefinition(
                name="sort_data",
                description="Sort data by specified field and order",
                category=FunctionCategory.DATA_PROCESSING,
                parameters=[
                    FunctionParameter("data", "list", "Input data to sort"),
                    FunctionParameter("field", "str", "Field to sort by"),
                    FunctionParameter("ascending", "bool", "Sort order", False, True),
                ],
                returns="list",
                examples=["Sort invoices by amount", "Sort users by registration date"]
            ),
            FunctionDefinition(
                name="aggregate_data",
                description="Aggregate data using specified operation",
                category=FunctionCategory.DATA_PROCESSING,
                parameters=[
                    FunctionParameter("data", "list", "Input data to aggregate"),
                    FunctionParameter("operation", "str", "Aggregation operation (sum, avg, count, etc.)"),
                    FunctionParameter("field", "str", "Field to aggregate", False),
                ],
                returns="float",
                examples=["Sum invoice amounts", "Count total records", "Average user age"]
            ),
            FunctionDefinition(
                name="group_data",
                description="Group data by specified field",
                category=FunctionCategory.DATA_PROCESSING,
                parameters=[
                    FunctionParameter("data", "list", "Input data to group"),
                    FunctionParameter("field", "str", "Field to group by"),
                ],
                returns="dict",
                examples=["Group invoices by month", "Group users by department"]
            ),
            FunctionDefinition(
                name="merge_datasets",
                description="Merge two datasets based on common field",
                category=FunctionCategory.DATA_PROCESSING,
                parameters=[
                    FunctionParameter("dataset1", "list", "First dataset"),
                    FunctionParameter("dataset2", "list", "Second dataset"),
                    FunctionParameter("join_field", "str", "Field to join on"),
                    FunctionParameter("join_type", "str", "Type of join (inner, left, right, outer)", False, "inner"),
                ],
                returns="list",
                examples=["Merge user data with order data", "Join invoice data with customer data"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_file_operations_functions(self):
        """Register file operations functions."""
        functions = [
            FunctionDefinition(
                name="read_file",
                description="Read content from a file",
                category=FunctionCategory.FILE_OPERATIONS,
                parameters=[
                    FunctionParameter("file_path", "str", "Path to the file"),
                    FunctionParameter("encoding", "str", "File encoding", False, "utf-8"),
                ],
                returns="str",
                examples=["Read CSV file", "Read text document", "Read configuration file"]
            ),
            FunctionDefinition(
                name="write_file",
                description="Write content to a file",
                category=FunctionCategory.FILE_OPERATIONS,
                parameters=[
                    FunctionParameter("file_path", "str", "Path to the file"),
                    FunctionParameter("content", "str", "Content to write"),
                    FunctionParameter("mode", "str", "Write mode (w, a, x)", False, "w"),
                ],
                returns="bool",
                examples=["Save report to file", "Write processed data", "Create backup file"]
            ),
            FunctionDefinition(
                name="copy_file",
                description="Copy a file from source to destination",
                category=FunctionCategory.FILE_OPERATIONS,
                parameters=[
                    FunctionParameter("source_path", "str", "Source file path"),
                    FunctionParameter("dest_path", "str", "Destination file path"),
                ],
                returns="bool",
                examples=["Backup important files", "Copy templates", "Duplicate documents"]
            ),
            FunctionDefinition(
                name="delete_file",
                description="Delete a file",
                category=FunctionCategory.FILE_OPERATIONS,
                parameters=[
                    FunctionParameter("file_path", "str", "Path to the file to delete"),
                ],
                returns="bool",
                examples=["Remove temporary files", "Clean up old logs", "Delete processed files"]
            ),
            FunctionDefinition(
                name="list_files",
                description="List files in a directory",
                category=FunctionCategory.FILE_OPERATIONS,
                parameters=[
                    FunctionParameter("directory_path", "str", "Directory path"),
                    FunctionParameter("pattern", "str", "File pattern filter", False, "*"),
                    FunctionParameter("recursive", "bool", "Search recursively", False, False),
                ],
                returns="list",
                examples=["List all CSV files", "Find log files", "Get all images in folder"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_communication_functions(self):
        """Register communication functions."""
        functions = [
            FunctionDefinition(
                name="send_email",
                description="Send an email message",
                category=FunctionCategory.COMMUNICATION,
                parameters=[
                    FunctionParameter("to", "str", "Recipient email address"),
                    FunctionParameter("subject", "str", "Email subject"),
                    FunctionParameter("body", "str", "Email body content"),
                    FunctionParameter("cc", "str", "CC recipients", False),
                    FunctionParameter("attachments", "list", "File attachments", False),
                ],
                returns="bool",
                examples=["Send invoice summary", "Email report to manager", "Send notification"]
            ),
            FunctionDefinition(
                name="send_sms",
                description="Send SMS message",
                category=FunctionCategory.COMMUNICATION,
                parameters=[
                    FunctionParameter("phone_number", "str", "Recipient phone number"),
                    FunctionParameter("message", "str", "SMS message content"),
                ],
                returns="bool",
                examples=["Send alert notification", "SMS reminder", "Emergency notification"]
            ),
            FunctionDefinition(
                name="make_api_call",
                description="Make HTTP API call",
                category=FunctionCategory.COMMUNICATION,
                parameters=[
                    FunctionParameter("url", "str", "API endpoint URL"),
                    FunctionParameter("method", "str", "HTTP method", False, "GET"),
                    FunctionParameter("headers", "dict", "Request headers", False),
                    FunctionParameter("data", "dict", "Request data", False),
                ],
                returns="dict",
                examples=["Call payment API", "Fetch user data", "Submit form data"]
            ),
            FunctionDefinition(
                name="post_to_slack",
                description="Post message to Slack channel",
                category=FunctionCategory.COMMUNICATION,
                parameters=[
                    FunctionParameter("channel", "str", "Slack channel name"),
                    FunctionParameter("message", "str", "Message content"),
                    FunctionParameter("username", "str", "Bot username", False),
                ],
                returns="bool",
                examples=["Post daily report", "Send team notification", "Alert on errors"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_database_functions(self):
        """Register database functions."""
        from .function_definitions import get_database_functions
        for func in get_database_functions():
            self.register_function(func)

    def _register_web_scraping_functions(self):
        """Register web scraping functions."""
        from .function_definitions import get_web_scraping_functions
        for func in get_web_scraping_functions():
            self.register_function(func)

    def _register_analytics_functions(self):
        """Register analytics functions."""
        from .function_definitions import get_analytics_functions
        for func in get_analytics_functions():
            self.register_function(func)

    def _register_authentication_functions(self):
        """Register authentication functions."""
        functions = [
            FunctionDefinition(
                name="authenticate_user",
                description="Authenticate user credentials",
                category=FunctionCategory.AUTHENTICATION,
                parameters=[
                    FunctionParameter("username", "str", "Username"),
                    FunctionParameter("password", "str", "Password"),
                    FunctionParameter("auth_method", "str", "Authentication method", False, "basic"),
                ],
                returns="dict",
                examples=["Login user", "Verify credentials", "API authentication"]
            ),
            FunctionDefinition(
                name="generate_token",
                description="Generate authentication token",
                category=FunctionCategory.AUTHENTICATION,
                parameters=[
                    FunctionParameter("user_id", "str", "User identifier"),
                    FunctionParameter("expiry_hours", "int", "Token expiry in hours", False, 24),
                ],
                returns="str",
                examples=["Create session token", "API access token", "Temporary access"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_scheduling_functions(self):
        """Register scheduling functions."""
        functions = [
            FunctionDefinition(
                name="schedule_task",
                description="Schedule a task for future execution",
                category=FunctionCategory.SCHEDULING,
                parameters=[
                    FunctionParameter("task_name", "str", "Name of the task"),
                    FunctionParameter("schedule_time", "str", "When to execute (datetime or cron)"),
                    FunctionParameter("function_call", "dict", "Function to execute"),
                ],
                returns="str",
                examples=["Schedule daily report", "Set reminder", "Automate backup"]
            ),
            FunctionDefinition(
                name="cancel_scheduled_task",
                description="Cancel a scheduled task",
                category=FunctionCategory.SCHEDULING,
                parameters=[
                    FunctionParameter("task_id", "str", "Task identifier"),
                ],
                returns="bool",
                examples=["Cancel reminder", "Stop recurring task", "Remove schedule"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_notification_functions(self):
        """Register notification functions."""
        functions = [
            FunctionDefinition(
                name="send_push_notification",
                description="Send push notification to mobile device",
                category=FunctionCategory.NOTIFICATION,
                parameters=[
                    FunctionParameter("device_token", "str", "Device token"),
                    FunctionParameter("title", "str", "Notification title"),
                    FunctionParameter("message", "str", "Notification message"),
                ],
                returns="bool",
                examples=["Alert user", "Send reminder", "Emergency notification"]
            ),
            FunctionDefinition(
                name="create_alert",
                description="Create system alert",
                category=FunctionCategory.NOTIFICATION,
                parameters=[
                    FunctionParameter("alert_type", "str", "Type of alert"),
                    FunctionParameter("message", "str", "Alert message"),
                    FunctionParameter("severity", "str", "Alert severity", False, "medium"),
                ],
                returns="str",
                examples=["System error alert", "Performance warning", "Security alert"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_conversion_functions(self):
        """Register conversion functions."""
        functions = [
            FunctionDefinition(
                name="convert_format",
                description="Convert data between formats",
                category=FunctionCategory.CONVERSION,
                parameters=[
                    FunctionParameter("data", "str", "Input data"),
                    FunctionParameter("from_format", "str", "Source format"),
                    FunctionParameter("to_format", "str", "Target format"),
                ],
                returns="str",
                examples=["CSV to JSON", "XML to dict", "PDF to text"]
            ),
            FunctionDefinition(
                name="encode_decode",
                description="Encode or decode data",
                category=FunctionCategory.CONVERSION,
                parameters=[
                    FunctionParameter("data", "str", "Input data"),
                    FunctionParameter("operation", "str", "encode or decode"),
                    FunctionParameter("encoding", "str", "Encoding type (base64, url, etc.)"),
                ],
                returns="str",
                examples=["Base64 encode", "URL decode", "Hash password"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_validation_functions(self):
        """Register validation functions."""
        functions = [
            FunctionDefinition(
                name="validate_data",
                description="Validate data against schema",
                category=FunctionCategory.VALIDATION,
                parameters=[
                    FunctionParameter("data", "dict", "Data to validate"),
                    FunctionParameter("schema", "dict", "Validation schema"),
                ],
                returns="dict",
                examples=["Validate form data", "Check API input", "Verify file format"]
            ),
            FunctionDefinition(
                name="check_data_quality",
                description="Check data quality and completeness",
                category=FunctionCategory.VALIDATION,
                parameters=[
                    FunctionParameter("data", "list", "Dataset to check"),
                    FunctionParameter("rules", "list", "Quality rules"),
                ],
                returns="dict",
                examples=["Data completeness check", "Duplicate detection", "Outlier analysis"]
            ),
        ]

        for func in functions:
            self.register_function(func)

    def _register_search_functions(self):
        """Register search functions."""
        functions = [
            FunctionDefinition(
                name="search_records",
                description="Search records in dataset",
                category=FunctionCategory.SEARCH,
                parameters=[
                    FunctionParameter("data", "list", "Dataset to search"),
                    FunctionParameter("query", "str", "Search query"),
                    FunctionParameter("fields", "list", "Fields to search in", False),
                ],
                returns="list",
                examples=["Find customer by name", "Search products", "Locate transactions"]
            ),
            FunctionDefinition(
                name="full_text_search",
                description="Perform full-text search",
                category=FunctionCategory.SEARCH,
                parameters=[
                    FunctionParameter("text", "str", "Text to search in"),
                    FunctionParameter("query", "str", "Search query"),
                    FunctionParameter("case_sensitive", "bool", "Case sensitive search", False, False),
                ],
                returns="list",
                examples=["Search documents", "Find keywords", "Content analysis"]
            ),
        ]

        for func in functions:
            self.register_function(func)
