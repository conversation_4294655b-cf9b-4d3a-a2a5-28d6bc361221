#pragma once

// @generated by torchgen/gen.py from NativeMetaFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/TensorIterator.h>
#include <ATen/TensorMeta.h>
#include <tuple>
#include <vector>

namespace at {
namespace meta {

struct TORCH_API structured_fractional_max_pool3d : public at::impl::MetaBase {
    
                template <bool POOLSIZET = false, bool POOLSIZEH = false, bool POOLSIZEW = false, bool OUTPUTT = false, bool OUTPUTH = false, bool OUTPUTW = false, bool NUMBATCH = false, bool NUMPLANES = false, bool INPUTT = false, bool INPUTH = false, bool INPUTW = false>
                struct TORCH_API precompute_out {
                    
                    precompute_out<true, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_poolSizeT(int64_t value) {
                        static_assert(POOLSIZET == false, "poolSizeT already set");
                        precompute_out<true, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = value;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, true, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_poolSizeH(int64_t value) {
                        static_assert(POOLSIZEH == false, "poolSizeH already set");
                        precompute_out<POOLSIZET, true, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = value;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, true, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_poolSizeW(int64_t value) {
                        static_assert(POOLSIZEW == false, "poolSizeW already set");
                        precompute_out<POOLSIZET, POOLSIZEH, true, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = value;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, true, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_outputT(int64_t value) {
                        static_assert(OUTPUTT == false, "outputT already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, true, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = value;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, true, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_outputH(int64_t value) {
                        static_assert(OUTPUTH == false, "outputH already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, true, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = value;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, true, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> set_outputW(int64_t value) {
                        static_assert(OUTPUTW == false, "outputW already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, true, NUMBATCH, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = value;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, true, NUMPLANES, INPUTT, INPUTH, INPUTW> set_numBatch(int64_t value) {
                        static_assert(NUMBATCH == false, "numBatch already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, true, NUMPLANES, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = value;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, true, INPUTT, INPUTH, INPUTW> set_numPlanes(int64_t value) {
                        static_assert(NUMPLANES == false, "numPlanes already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, true, INPUTT, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = value;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, true, INPUTH, INPUTW> set_inputT(int64_t value) {
                        static_assert(INPUTT == false, "inputT already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, true, INPUTH, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = value;
ret.inputH = this->inputH;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, true, INPUTW> set_inputH(int64_t value) {
                        static_assert(INPUTH == false, "inputH already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, true, INPUTW> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = value;
ret.inputW = this->inputW;
return ret;
                    }
                

                    precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, true> set_inputW(int64_t value) {
                        static_assert(INPUTW == false, "inputW already set");
                        precompute_out<POOLSIZET, POOLSIZEH, POOLSIZEW, OUTPUTT, OUTPUTH, OUTPUTW, NUMBATCH, NUMPLANES, INPUTT, INPUTH, true> ret;
ret.poolSizeT = this->poolSizeT;
ret.poolSizeH = this->poolSizeH;
ret.poolSizeW = this->poolSizeW;
ret.outputT = this->outputT;
ret.outputH = this->outputH;
ret.outputW = this->outputW;
ret.numBatch = this->numBatch;
ret.numPlanes = this->numPlanes;
ret.inputT = this->inputT;
ret.inputH = this->inputH;
ret.inputW = value;
return ret;
                    }
                
                    int64_t poolSizeT;
int64_t poolSizeH;
int64_t poolSizeW;
int64_t outputT;
int64_t outputH;
int64_t outputW;
int64_t numBatch;
int64_t numPlanes;
int64_t inputT;
int64_t inputH;
int64_t inputW;
            };
    using meta_return_ty = precompute_out <true, true, true, true, true, true, true, true, true, true, true>;
    meta_return_ty meta(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples);
};

} // namespace native
} // namespace at
