#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/split_ops.h>

namespace at {


// aten::split.Tensor(Tensor(a -> *) self, SymInt split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> split(const at::Tensor & self, int64_t split_size, int64_t dim=0) {
    return at::_ops::split_Tensor::call(self, split_size, dim);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  ::std::vector<at::Tensor> split(const at::Tensor & self, int64_t split_size, int64_t dim=0) {
    return at::_ops::split_Tensor::call(self, split_size, dim);
  }
}

// aten::split.Tensor(Tensor(a -> *) self, SymInt split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> split_symint(const at::Tensor & self, c10::SymInt split_size, int64_t dim=0) {
    return at::_ops::split_Tensor::call(self, split_size, dim);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  ::std::vector<at::Tensor> split(const at::Tensor & self, c10::SymInt split_size, int64_t dim=0) {
    return at::_ops::split_Tensor::call(self, split_size, dim);
  }
}

// aten::split.sizes(Tensor(a -> *) self, SymInt[] split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> split(const at::Tensor & self, at::IntArrayRef split_size, int64_t dim=0) {
    return at::_ops::split_sizes::call(self, c10::fromIntArrayRefSlow(split_size), dim);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  ::std::vector<at::Tensor> split(const at::Tensor & self, at::IntArrayRef split_size, int64_t dim=0) {
    return at::_ops::split_sizes::call(self, c10::fromIntArrayRefSlow(split_size), dim);
  }
}

// aten::split.sizes(Tensor(a -> *) self, SymInt[] split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> split_symint(const at::Tensor & self, c10::SymIntArrayRef split_size, int64_t dim=0) {
    return at::_ops::split_sizes::call(self, split_size, dim);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  ::std::vector<at::Tensor> split(const at::Tensor & self, c10::SymIntArrayRef split_size, int64_t dim=0) {
    return at::_ops::split_sizes::call(self, split_size, dim);
  }
}

}
