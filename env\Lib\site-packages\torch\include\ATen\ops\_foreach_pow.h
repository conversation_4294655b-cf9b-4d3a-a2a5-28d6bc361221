#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_foreach_pow_ops.h>

namespace at {


// aten::_foreach_pow.List(Tensor[] self, Tensor[] exponent) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_pow(at::TensorList self, at::TensorList exponent) {
    return at::_ops::_foreach_pow_List::call(self, exponent);
}

// aten::_foreach_pow.Scalar(Tensor[] self, Scalar exponent) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_pow(at::TensorList self, const at::Scalar & exponent) {
    return at::_ops::_foreach_pow_Scalar::call(self, exponent);
}

// aten::_foreach_pow.ScalarList(Tensor[] self, Scalar[] exponent) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_pow(at::TensorList self, at::ArrayRef<at::Scalar> exponent) {
    return at::_ops::_foreach_pow_ScalarList::call(self, exponent);
}

// aten::_foreach_pow.ScalarAndTensor(Scalar self, Tensor[] exponent) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_pow(const at::Scalar & self, at::TensorList exponent) {
    return at::_ops::_foreach_pow_ScalarAndTensor::call(self, exponent);
}

// aten::_foreach_pow_.List(Tensor(a!)[] self, Tensor[] exponent) -> ()
inline void _foreach_pow_(at::TensorList self, at::TensorList exponent) {
    return at::_ops::_foreach_pow__List::call(self, exponent);
}

// aten::_foreach_pow_.Scalar(Tensor(a!)[] self, Scalar exponent) -> ()
inline void _foreach_pow_(at::TensorList self, const at::Scalar & exponent) {
    return at::_ops::_foreach_pow__Scalar::call(self, exponent);
}

// aten::_foreach_pow_.ScalarList(Tensor(a!)[] self, Scalar[] exponent) -> ()
inline void _foreach_pow_(at::TensorList self, at::ArrayRef<at::Scalar> exponent) {
    return at::_ops::_foreach_pow__ScalarList::call(self, exponent);
}

// aten::_foreach_pow.List_out(Tensor[] self, Tensor[] exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_out(at::TensorList out, at::TensorList self, at::TensorList exponent) {
    return at::_ops::_foreach_pow_List_out::call(self, exponent, out);
}
// aten::_foreach_pow.List_out(Tensor[] self, Tensor[] exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_outf(at::TensorList self, at::TensorList exponent, at::TensorList out) {
    return at::_ops::_foreach_pow_List_out::call(self, exponent, out);
}

// aten::_foreach_pow.Scalar_out(Tensor[] self, Scalar exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_out(at::TensorList out, at::TensorList self, const at::Scalar & exponent) {
    return at::_ops::_foreach_pow_Scalar_out::call(self, exponent, out);
}
// aten::_foreach_pow.Scalar_out(Tensor[] self, Scalar exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_outf(at::TensorList self, const at::Scalar & exponent, at::TensorList out) {
    return at::_ops::_foreach_pow_Scalar_out::call(self, exponent, out);
}

// aten::_foreach_pow.ScalarList_out(Tensor[] self, Scalar[] exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_out(at::TensorList out, at::TensorList self, at::ArrayRef<at::Scalar> exponent) {
    return at::_ops::_foreach_pow_ScalarList_out::call(self, exponent, out);
}
// aten::_foreach_pow.ScalarList_out(Tensor[] self, Scalar[] exponent, *, Tensor(a!)[] out) -> ()
inline void _foreach_pow_outf(at::TensorList self, at::ArrayRef<at::Scalar> exponent, at::TensorList out) {
    return at::_ops::_foreach_pow_ScalarList_out::call(self, exponent, out);
}

}
