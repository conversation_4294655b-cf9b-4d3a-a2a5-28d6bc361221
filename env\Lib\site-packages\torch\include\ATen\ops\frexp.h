#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/frexp_ops.h>

namespace at {


// aten::frexp.Tensor(Tensor self) -> (Tensor mantissa, Tensor exponent)
inline ::std::tuple<at::Tensor,at::Tensor> frexp(const at::Tensor & self) {
    return at::_ops::frexp_Tensor::call(self);
}

// aten::frexp.Tensor_out(Tensor self, *, <PERSON><PERSON>(a!) mantissa, Tensor(b!) exponent) -> (Tensor(a!) mantissa, Tensor(b!) exponent)
inline ::std::tuple<at::Tensor &,at::Tensor &> frexp_out(at::Tensor & mantissa, at::Tensor & exponent, const at::Tensor & self) {
    return at::_ops::frexp_Tensor_out::call(self, mantissa, exponent);
}
// aten::frexp.Tensor_out(Tensor self, *, Tensor(a!) mantissa, Tensor(b!) exponent) -> (Tensor(a!) mantissa, Tensor(b!) exponent)
inline ::std::tuple<at::Tensor &,at::Tensor &> frexp_outf(const at::Tensor & self, at::Tensor & mantissa, at::Tensor & exponent) {
    return at::_ops::frexp_Tensor_out::call(self, mantissa, exponent);
}

}
