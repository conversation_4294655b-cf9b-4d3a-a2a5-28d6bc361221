#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _pad_enum {
  using schema = at::Tensor (const at::Tensor &, c10::SymIntArrayRef, int64_t, ::std::optional<double>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_pad_enum";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_pad_enum(Tensor self, SymInt[] pad, int mode, float? value=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, c10::SymIntArrayRef pad, int64_t mode, ::std::optional<double> value);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, c10::SymIntArrayRef pad, int64_t mode, ::std::optional<double> value);
};

}} // namespace at::_ops
