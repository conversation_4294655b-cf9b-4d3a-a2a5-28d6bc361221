"""
SMS Implementation - Simple SMS sending functionality.

This module provides a basic SMS implementation that can be extended
to work with various SMS providers like Twilio, AWS SNS, etc.
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime


class SMSProvider:
    """Base SMS provider interface."""
    
    def send_sms(self, to: str, message: str, from_number: Optional[str] = None) -> Dict[str, Any]:
        """Send SMS message."""
        raise NotImplementedError("Subclasses must implement send_sms")


class MockSMSProvider(SMSProvider):
    """Mock SMS provider for testing and demonstration."""
    
    def __init__(self):
        self.sent_messages = []
        self.logger = logging.getLogger(__name__)
    
    def send_sms(self, to: str, message: str, from_number: Optional[str] = None) -> Dict[str, Any]:
        """Send SMS message (mock implementation)."""
        try:
            # Validate phone number (basic validation)
            if not to or not to.isdigit() or len(to) < 10:
                return {
                    "success": False,
                    "error": "Invalid phone number format",
                    "message_id": None
                }
            
            # Validate message
            if not message or len(message.strip()) == 0:
                return {
                    "success": False,
                    "error": "Message cannot be empty",
                    "message_id": None
                }
            
            # Create message record
            message_record = {
                "to": to,
                "message": message,
                "from_number": from_number or "SYSTEM",
                "timestamp": datetime.now().isoformat(),
                "message_id": f"msg_{len(self.sent_messages) + 1:06d}",
                "status": "sent"
            }
            
            # Store message
            self.sent_messages.append(message_record)
            
            # Log the message
            self.logger.info(f"📱 SMS sent to {to}: {message}")
            
            # Print for demonstration
            print(f"📱 SMS SENT:")
            print(f"   To: {to}")
            print(f"   Message: {message}")
            print(f"   From: {from_number or 'SYSTEM'}")
            print(f"   Message ID: {message_record['message_id']}")
            print(f"   Status: ✅ Delivered")
            
            return {
                "success": True,
                "message_id": message_record["message_id"],
                "status": "sent",
                "to": to,
                "message": message
            }
            
        except Exception as e:
            self.logger.error(f"Failed to send SMS: {e}")
            return {
                "success": False,
                "error": str(e),
                "message_id": None
            }
    
    def get_sent_messages(self) -> list:
        """Get list of sent messages."""
        return self.sent_messages.copy()
    
    def get_message_status(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific message."""
        for msg in self.sent_messages:
            if msg["message_id"] == message_id:
                return msg
        return None


class TwilioSMSProvider(SMSProvider):
    """Twilio SMS provider (requires Twilio SDK)."""
    
    def __init__(self, account_sid: str, auth_token: str, from_number: str):
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.from_number = from_number
        self.logger = logging.getLogger(__name__)
        
        # Try to import Twilio (optional dependency)
        try:
            from twilio.rest import Client
            self.client = Client(account_sid, auth_token)
        except ImportError:
            self.logger.warning("Twilio SDK not installed. Using mock implementation.")
            self.client = None
    
    def send_sms(self, to: str, message: str, from_number: Optional[str] = None) -> Dict[str, Any]:
        """Send SMS using Twilio."""
        if not self.client:
            # Fall back to mock implementation
            mock_provider = MockSMSProvider()
            return mock_provider.send_sms(to, message, from_number)
        
        try:
            # Format phone number
            if not to.startswith('+'):
                to = f"+1{to}"  # Assume US number if no country code
            
            from_num = from_number or self.from_number
            if not from_num.startswith('+'):
                from_num = f"+1{from_num}"
            
            # Send SMS via Twilio
            message_obj = self.client.messages.create(
                body=message,
                from_=from_num,
                to=to
            )
            
            return {
                "success": True,
                "message_id": message_obj.sid,
                "status": message_obj.status,
                "to": to,
                "message": message
            }
            
        except Exception as e:
            self.logger.error(f"Twilio SMS failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message_id": None
            }


# Default SMS provider instance
_default_provider = MockSMSProvider()


def send_sms(to: str, message: str, from_number: Optional[str] = None) -> Dict[str, Any]:
    """Send SMS using the default provider."""
    return _default_provider.send_sms(to, message, from_number)


def set_sms_provider(provider: SMSProvider):
    """Set the default SMS provider."""
    global _default_provider
    _default_provider = provider


def get_sms_provider() -> SMSProvider:
    """Get the current SMS provider."""
    return _default_provider


# Function for the pipeline
def execute_send_sms(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Execute SMS sending function for the pipeline."""
    to = parameters.get("to")
    message = parameters.get("message")
    from_number = parameters.get("from_number")
    
    if not to or not message:
        return {
            "success": False,
            "error": "Missing required parameters: 'to' and 'message'",
            "result": None
        }
    
    result = send_sms(to, message, from_number)
    
    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result
    }
