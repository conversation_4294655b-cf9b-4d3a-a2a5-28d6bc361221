#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API nonzero_static_out {
  using schema = at::Tensor & (const at::Tensor &, c10::SymInt, int64_t, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nonzero_static";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "nonzero_static.out(Tensor self, *, SymInt size, int fill_value=-1, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, c10::SymInt size, int64_t fill_value, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, c10::SymInt size, int64_t fill_value, at::Tensor & out);
};

struct TORCH_API nonzero_static {
  using schema = at::Tensor (const at::Tensor &, c10::SymInt, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nonzero_static";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "nonzero_static(Tensor self, *, SymInt size, int fill_value=-1) -> Tensor";
  static at::Tensor call(const at::Tensor & self, c10::SymInt size, int64_t fill_value);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, c10::SymInt size, int64_t fill_value);
};

}} // namespace at::_ops
