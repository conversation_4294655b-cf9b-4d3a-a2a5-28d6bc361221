#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/swapdims_ops.h>

namespace at {


// aten::swapdims(Tensor(a) self, int dim0, int dim1) -> Tensor(a)
inline at::Tensor swapdims(const at::Tensor & self, int64_t dim0, int64_t dim1) {
    return at::_ops::swapdims::call(self, dim0, dim1);
}

}
