#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _histogramdd_from_bin_tensors {
  using schema = at::Tensor (const at::Tensor &, at::TensorList, const ::std::optional<at::Tensor> &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_histogramdd_from_bin_tensors";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_histogramdd_from_bin_tensors(Tensor self, Tensor[] bins, *, Tensor? weight=None, bool density=False) -> Tensor";
  static at::Tensor call(const at::Tensor & self, at::TensorList bins, const ::std::optional<at::Tensor> & weight, bool density);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::TensorList bins, const ::std::optional<at::Tensor> & weight, bool density);
};

struct TORCH_API _histogramdd_from_bin_tensors_out {
  using schema = at::Tensor & (const at::Tensor &, at::TensorList, const ::std::optional<at::Tensor> &, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_histogramdd_from_bin_tensors";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_histogramdd_from_bin_tensors.out(Tensor self, Tensor[] bins, *, Tensor? weight=None, bool density=False, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, at::TensorList bins, const ::std::optional<at::Tensor> & weight, bool density, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::TensorList bins, const ::std::optional<at::Tensor> & weight, bool density, at::Tensor & out);
};

}} // namespace at::_ops
