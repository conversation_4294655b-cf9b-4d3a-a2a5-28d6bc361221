# @generated by update_schema.py
# checksum<<31c433c768b3f1bb61a5e8f4ceffc40c857bd80cf4fa0fc33fd03fa5ebb6c4d8>>
AOTInductorModelPickleData:
  kind: struct
  fields:
    library_basename:
      type: str
    input_names:
      type: List[str]
    output_names:
      type: List[str]
    floating_point_input_dtype:
      type: Optional[int]
      default: None
    floating_point_output_dtype:
      type: Optional[int]
      default: None
    aot_inductor_model_is_cpu:
      type: Optional[bool]
      default: None
Argument:
  kind: union
  fields:
    as_none:
      type: bool
    as_tensor:
      type: TensorArgument
    as_tensors:
      type: List[TensorArgument]
    as_int:
      type: int
    as_ints:
      type: List[int]
    as_float:
      type: float
    as_floats:
      type: List[float]
    as_string:
      type: str
    as_strings:
      type: List[str]
    as_sym_int:
      type: SymIntArgument
    as_sym_ints:
      type: List[SymIntArgument]
    as_scalar_type:
      type: ScalarType
    as_memory_format:
      type: MemoryFormat
    as_layout:
      type: Layout
    as_device:
      type: Device
    as_bool:
      type: bool
    as_bools:
      type: List[bool]
    as_sym_bool:
      type: SymBoolArgument
    as_sym_bools:
      type: List[SymBoolArgument]
    as_graph:
      type: GraphArgument
    as_optional_tensors:
      type: List[OptionalTensorArgument]
    as_custom_obj:
      type: CustomObjArgument
    as_operator:
      type: str
    as_sym_float:
      type: SymFloatArgument
    as_sym_floats:
      type: List[SymFloatArgument]
ArgumentKind:
  kind: enum
  fields:
    UNKNOWN: 0
    POSITIONAL: 1
    KEYWORD: 2
BufferMutationSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    buffer_name:
      type: str
ConstantValue:
  kind: union
  fields:
    as_none:
      type: bool
    as_int:
      type: int
    as_float:
      type: float
    as_string:
      type: str
    as_bool:
      type: bool
CustomObjArgument:
  kind: struct
  fields:
    name:
      type: str
    class_fqn:
      type: str
Device:
  kind: struct
  fields:
    type:
      type: str
    index:
      type: Optional[int]
      default: None
ExportedProgram:
  kind: struct
  fields:
    graph_module:
      type: GraphModule
    opset_version:
      type: Dict[str, int]
    range_constraints:
      type: Dict[str, RangeConstraint]
    schema_version:
      type: SchemaVersion
    verifiers:
      type: List[str]
      default: '[]'
    torch_version:
      type: str
      default: <=2.4
ExternKernelNode:
  kind: struct
  fields:
    name:
      type: str
    node:
      type: Node
ExternKernelNodes:
  kind: struct
  fields:
    nodes:
      type: List[ExternKernelNode]
GradientToParameterSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    parameter_name:
      type: str
GradientToUserInputSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    user_input_name:
      type: str
Graph:
  kind: struct
  fields:
    inputs:
      type: List[Argument]
    outputs:
      type: List[Argument]
    nodes:
      type: List[Node]
    tensor_values:
      type: Dict[str, TensorMeta]
    sym_int_values:
      type: Dict[str, SymInt]
    sym_bool_values:
      type: Dict[str, SymBool]
    is_single_tensor_return:
      type: bool
      default: 'False'
    custom_obj_values:
      type: Dict[str, CustomObjArgument]
      default: '{}'
    sym_float_values:
      type: Dict[str, SymFloat]
      default: '{}'
GraphArgument:
  kind: struct
  fields:
    name:
      type: str
    graph:
      type: Graph
GraphModule:
  kind: struct
  fields:
    graph:
      type: Graph
    signature:
      type: GraphSignature
    module_call_graph:
      type: List[ModuleCallEntry]
    metadata:
      type: Dict[str, str]
      default: '{}'
    treespec_namedtuple_fields:
      type: Dict[str, NamedTupleDef]
      default: '{}'
GraphSignature:
  kind: struct
  fields:
    input_specs:
      type: List[InputSpec]
    output_specs:
      type: List[OutputSpec]
InputSpec:
  kind: union
  fields:
    user_input:
      type: UserInputSpec
    parameter:
      type: InputToParameterSpec
    buffer:
      type: InputToBufferSpec
    tensor_constant:
      type: InputToTensorConstantSpec
    custom_obj:
      type: InputToCustomObjSpec
    token:
      type: InputTokenSpec
    constant_input:
      type: InputToConstantInputSpec
InputToBufferSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    buffer_name:
      type: str
    persistent:
      type: bool
InputToConstantInputSpec:
  kind: struct
  fields:
    name:
      type: str
    value:
      type: ConstantValue
InputToCustomObjSpec:
  kind: struct
  fields:
    arg:
      type: CustomObjArgument
    custom_obj_name:
      type: str
InputToParameterSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    parameter_name:
      type: str
InputToTensorConstantSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    tensor_constant_name:
      type: str
InputTokenSpec:
  kind: struct
  fields:
    arg:
      type: TokenArgument
Layout:
  kind: enum
  fields:
    Unknown: 0
    SparseCoo: 1
    SparseCsr: 2
    SparseCsc: 3
    SparseBsr: 4
    SparseBsc: 5
    _mkldnn: 6
    Strided: 7
LossOutputSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
MemoryFormat:
  kind: enum
  fields:
    Unknown: 0
    ContiguousFormat: 1
    ChannelsLast: 2
    ChannelsLast3d: 3
    PreserveFormat: 4
Model:
  kind: struct
  fields:
    name:
      type: str
    tensorPaths:
      type: Dict[str, str]
    program:
      type: Program
    delegates:
      type: Dict[str, Program]
    deviceAllocationMap:
      type: Dict[str, str]
    constantPaths:
      type: Dict[str, str]
ModuleCallEntry:
  kind: struct
  fields:
    fqn:
      type: str
    signature:
      type: Optional[ModuleCallSignature]
      default: None
ModuleCallSignature:
  kind: struct
  fields:
    inputs:
      type: List[Argument]
    outputs:
      type: List[Argument]
    in_spec:
      type: str
    out_spec:
      type: str
    forward_arg_names:
      type: Optional[List[str]]
      default: None
NamedArgument:
  kind: struct
  fields:
    name:
      type: str
    arg:
      type: Argument
    kind:
      type: Optional[ArgumentKind]
      default: None
NamedTupleDef:
  kind: struct
  fields:
    field_names:
      type: List[str]
Node:
  kind: struct
  fields:
    target:
      type: str
    inputs:
      type: List[NamedArgument]
    outputs:
      type: List[Argument]
    metadata:
      type: Dict[str, str]
    is_hop_single_tensor_return:
      type: Optional[bool]
      default: None
OptionalTensorArgument:
  kind: union
  fields:
    as_tensor:
      type: TensorArgument
    as_none:
      type: bool
OutputSpec:
  kind: union
  fields:
    user_output:
      type: UserOutputSpec
    loss_output:
      type: LossOutputSpec
    buffer_mutation:
      type: BufferMutationSpec
    gradient_to_parameter:
      type: GradientToParameterSpec
    gradient_to_user_input:
      type: GradientToUserInputSpec
    user_input_mutation:
      type: UserInputMutationSpec
    token:
      type: OutputTokenSpec
OutputTokenSpec:
  kind: struct
  fields:
    arg:
      type: TokenArgument
Program:
  kind: struct
  fields:
    methods:
      type: Dict[str, ExportedProgram]
RangeConstraint:
  kind: struct
  fields:
    min_val:
      type: Optional[int]
    max_val:
      type: Optional[int]
ScalarType:
  kind: enum
  fields:
    UNKNOWN: 0
    BYTE: 1
    CHAR: 2
    SHORT: 3
    INT: 4
    LONG: 5
    HALF: 6
    FLOAT: 7
    DOUBLE: 8
    COMPLEXHALF: 9
    COMPLEXFLOAT: 10
    COMPLEXDOUBLE: 11
    BOOL: 12
    BFLOAT16: 13
    UINT16: 28
    FLOAT8E4M3FN: 29
    FLOAT8E5M2: 30
SchemaVersion:
  kind: struct
  fields:
    major:
      type: int
    minor:
      type: int
SymBool:
  kind: union
  fields:
    as_expr:
      type: SymExpr
    as_bool:
      type: bool
SymBoolArgument:
  kind: union
  fields:
    as_name:
      type: str
    as_bool:
      type: bool
SymExpr:
  kind: struct
  fields:
    expr_str:
      type: str
    hint:
      type: Optional[SymExprHint]
      default: None
SymExprHint:
  kind: union
  fields:
    as_int:
      type: int
    as_bool:
      type: bool
    as_float:
      type: float
SymFloat:
  kind: union
  fields:
    as_expr:
      type: SymExpr
    as_float:
      type: float
SymFloatArgument:
  kind: union
  fields:
    as_name:
      type: str
    as_float:
      type: float
SymInt:
  kind: union
  fields:
    as_expr:
      type: SymExpr
    as_int:
      type: int
SymIntArgument:
  kind: union
  fields:
    as_name:
      type: str
    as_int:
      type: int
TensorArgument:
  kind: struct
  fields:
    name:
      type: str
TensorMeta:
  kind: struct
  fields:
    dtype:
      type: ScalarType
    sizes:
      type: List[SymInt]
    requires_grad:
      type: bool
    device:
      type: Device
    strides:
      type: List[SymInt]
    storage_offset:
      type: SymInt
    layout:
      type: Layout
TokenArgument:
  kind: struct
  fields:
    name:
      type: str
UserInputMutationSpec:
  kind: struct
  fields:
    arg:
      type: TensorArgument
    user_input_name:
      type: str
UserInputSpec:
  kind: struct
  fields:
    arg:
      type: Argument
UserOutputSpec:
  kind: struct
  fields:
    arg:
      type: Argument
SCHEMA_VERSION:
- 8
- 7
TREESPEC_VERSION: 1
