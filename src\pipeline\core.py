"""
Core Pipeline Implementation - Main pipeline for processing queries and generating function call sequences.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from ..models.model_interface import ModelInterface, ModelFactory, ModelConfig, ExecutionPlan
    from ..functions.function_registry import FunctionRegistry
    from ..parsers.query_parser import QueryParser
    from ..utils.execution_engine import ExecutionEngine
except ImportError:
    # Handle case when running as script
    from models.model_interface import ModelInterface, ModelFactory, ModelConfig, ExecutionPlan
    from functions.function_registry import FunctionRegistry
    from parsers.query_parser import QueryParser
    from utils.execution_engine import ExecutionEngine


@dataclass
class PipelineResult:
    """Result from pipeline processing."""
    query: str
    execution_plan: ExecutionPlan
    success: bool
    error_message: Optional[str] = None
    execution_results: Optional[List[Dict[str, Any]]] = None


class FunctionCallPipeline:
    """Main pipeline for processing natural language queries into function call sequences."""
    
    def __init__(self, 
                 model_config: Optional[ModelConfig] = None,
                 function_registry: Optional[FunctionRegistry] = None,
                 enable_execution: bool = False):
        """
        Initialize the pipeline.
        
        Args:
            model_config: Configuration for the AI model
            function_registry: Registry of available functions
            enable_execution: Whether to actually execute functions (default: False for safety)
        """
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.model_config = model_config or self._get_default_model_config()
        self.model = ModelFactory.create_model(self.model_config)
        self.function_registry = function_registry or FunctionRegistry()
        self.query_parser = QueryParser()
        self.execution_engine = ExecutionEngine() if enable_execution else None
        
        self.logger.info(f"Pipeline initialized with model: {self.model_config.model_name}")
    
    def process_query(self, query: str, **kwargs) -> PipelineResult:
        """
        Process a natural language query and return execution plan.
        
        Args:
            query: Natural language query
            **kwargs: Additional parameters for processing
            
        Returns:
            PipelineResult containing execution plan and results
        """
        try:
            self.logger.info(f"Processing query: {query}")
            
            # Step 1: Parse and analyze the query
            parsed_query = self.query_parser.parse(query)
            self.logger.debug(f"Parsed query: {parsed_query}")
            
            # Step 2: Get available functions
            available_functions = self._get_relevant_functions(parsed_query)
            function_names = [func.name for func in available_functions]
            
            # Step 3: Extract function calls using AI model
            function_calls = self.model.extract_function_calls(query, function_names)
            self.logger.info(f"Extracted {len(function_calls)} function calls")
            
            # Step 4: Create execution plan
            execution_plan = self.model.create_execution_plan(query, function_calls)
            self.logger.info(f"Created execution plan with {len(execution_plan.function_calls)} steps")
            
            # Step 5: Validate the execution plan
            validation_result = self._validate_execution_plan(execution_plan)
            if not validation_result["valid"]:
                return PipelineResult(
                    query=query,
                    execution_plan=execution_plan,
                    success=False,
                    error_message=f"Invalid execution plan: {validation_result['error']}"
                )
            
            # Step 6: Execute if enabled
            execution_results = None
            if self.execution_engine and kwargs.get("execute", False):
                execution_results = self.execution_engine.execute_plan(execution_plan)
            
            return PipelineResult(
                query=query,
                execution_plan=execution_plan,
                success=True,
                execution_results=execution_results
            )
            
        except Exception as e:
            self.logger.error(f"Error processing query: {e}")
            return PipelineResult(
                query=query,
                execution_plan=ExecutionPlan([], {}, "", 0.0),
                success=False,
                error_message=str(e)
            )
    
    def get_available_functions(self) -> List[str]:
        """Get list of all available function names."""
        return [func.name for func in self.function_registry.get_all_functions()]
    
    def search_functions(self, search_term: str) -> List[str]:
        """Search for functions by name or description."""
        results = self.function_registry.search_functions(search_term)
        return [func.name for func in results]
    
    def explain_plan(self, execution_plan: ExecutionPlan) -> str:
        """Generate human-readable explanation of execution plan."""
        if not execution_plan.function_calls:
            return "No functions to execute."
        
        explanation = f"Execution Plan (Confidence: {execution_plan.confidence:.2f}):\n"
        explanation += f"Reasoning: {execution_plan.reasoning}\n\n"
        
        for i, call in enumerate(execution_plan.function_calls, 1):
            explanation += f"{i}. {call.function_name}({call.parameters})\n"
            if call.reasoning:
                explanation += f"   Reason: {call.reasoning}\n"
            
            # Show dependencies
            deps = execution_plan.dependencies.get(call.function_name, [])
            if deps:
                explanation += f"   Depends on: {', '.join(deps)}\n"
            explanation += "\n"
        
        return explanation
    
    def _get_default_model_config(self) -> ModelConfig:
        """Get default model configuration."""
        from ..models.model_interface import DEFAULT_MODELS
        return DEFAULT_MODELS["mistral-7b"]  # Default to Mistral 7B
    
    def _get_relevant_functions(self, parsed_query: Dict[str, Any]) -> List:
        """Get functions relevant to the parsed query."""
        # For now, return all functions. In a more sophisticated implementation,
        # this could use semantic similarity or keyword matching to filter functions.
        return self.function_registry.get_all_functions()
    
    def _validate_execution_plan(self, execution_plan: ExecutionPlan) -> Dict[str, Any]:
        """Validate the execution plan for correctness."""
        try:
            # Check if all functions exist
            available_functions = {func.name for func in self.function_registry.get_all_functions()}
            
            for call in execution_plan.function_calls:
                if call.function_name not in available_functions:
                    return {
                        "valid": False,
                        "error": f"Unknown function: {call.function_name}"
                    }
            
            # Check dependency cycles
            if self._has_dependency_cycle(execution_plan.dependencies):
                return {
                    "valid": False,
                    "error": "Circular dependency detected in execution plan"
                }
            
            # Validate function parameters (basic check)
            for call in execution_plan.function_calls:
                func_def = self.function_registry.get_function(call.function_name)
                if func_def:
                    required_params = {p.name for p in func_def.parameters if p.required}
                    provided_params = set(call.parameters.keys())
                    missing_params = required_params - provided_params
                    
                    if missing_params:
                        return {
                            "valid": False,
                            "error": f"Missing required parameters for {call.function_name}: {missing_params}"
                        }
            
            return {"valid": True}
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Validation error: {e}"
            }
    
    def _has_dependency_cycle(self, dependencies: Dict[str, List[str]]) -> bool:
        """Check if there are circular dependencies."""
        def has_cycle(node, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in dependencies.get(node, []):
                if neighbor not in visited:
                    if has_cycle(neighbor, visited, rec_stack):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node)
            return False
        
        visited = set()
        for node in dependencies:
            if node not in visited:
                if has_cycle(node, visited, set()):
                    return True
        
        return False


class PipelineBuilder:
    """Builder class for creating configured pipelines."""
    
    def __init__(self):
        self.model_config = None
        self.function_registry = None
        self.enable_execution = False
    
    def with_model(self, model_config: ModelConfig) -> "PipelineBuilder":
        """Set the model configuration."""
        self.model_config = model_config
        return self
    
    def with_functions(self, function_registry: FunctionRegistry) -> "PipelineBuilder":
        """Set the function registry."""
        self.function_registry = function_registry
        return self
    
    def with_execution(self, enabled: bool = True) -> "PipelineBuilder":
        """Enable or disable function execution."""
        self.enable_execution = enabled
        return self
    
    def build(self) -> FunctionCallPipeline:
        """Build the pipeline."""
        return FunctionCallPipeline(
            model_config=self.model_config,
            function_registry=self.function_registry,
            enable_execution=self.enable_execution
        )
