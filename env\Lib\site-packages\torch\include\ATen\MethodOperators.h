#pragma once

// @generated by torchgen/gen.py from MethodOperators.h

#ifdef TORCH_ASSERT_NO_OPERATORS
#error This change adds a dependency on native_functions.yaml,             \
  meaning the file will need to be re-compiled every time an operator      \
  is changed or added. Consider if your change would be better placed in   \
  another file, or if a more specific header might achieve the same goal.  \
  See NOTE: [Tensor vs. TensorBase]
#endif

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

#include <ATen/ops/_addmm_activation_ops.h>
#include <ATen/ops/_autocast_to_full_precision_ops.h>
#include <ATen/ops/_autocast_to_reduced_precision_ops.h>
#include <ATen/ops/_backward_ops.h>
#include <ATen/ops/_coalesced_ops.h>
#include <ATen/ops/_conj_ops.h>
#include <ATen/ops/_conj_physical_ops.h>
#include <ATen/ops/_dimI_ops.h>
#include <ATen/ops/_dimV_ops.h>
#include <ATen/ops/_fw_primal_ops.h>
#include <ATen/ops/_indices_ops.h>
#include <ATen/ops/_is_all_true_ops.h>
#include <ATen/ops/_is_any_true_ops.h>
#include <ATen/ops/_is_zerotensor_ops.h>
#include <ATen/ops/_lazy_clone_ops.h>
#include <ATen/ops/_neg_view_ops.h>
#include <ATen/ops/_nested_tensor_size_ops.h>
#include <ATen/ops/_nested_tensor_storage_offsets_ops.h>
#include <ATen/ops/_nested_tensor_strides_ops.h>
#include <ATen/ops/_nnz_ops.h>
#include <ATen/ops/_reshape_alias_ops.h>
#include <ATen/ops/_sparse_mask_projection_ops.h>
#include <ATen/ops/_to_dense_ops.h>
#include <ATen/ops/_to_sparse_bsc_ops.h>
#include <ATen/ops/_to_sparse_bsr_ops.h>
#include <ATen/ops/_to_sparse_csc_ops.h>
#include <ATen/ops/_to_sparse_csr_ops.h>
#include <ATen/ops/_to_sparse_ops.h>
#include <ATen/ops/_values_ops.h>
#include <ATen/ops/_version_ops.h>
#include <ATen/ops/abs_ops.h>
#include <ATen/ops/absolute_ops.h>
#include <ATen/ops/acos_ops.h>
#include <ATen/ops/acosh_ops.h>
#include <ATen/ops/add_ops.h>
#include <ATen/ops/addbmm_ops.h>
#include <ATen/ops/addcdiv_ops.h>
#include <ATen/ops/addcmul_ops.h>
#include <ATen/ops/addmm_ops.h>
#include <ATen/ops/addmv_ops.h>
#include <ATen/ops/addr_ops.h>
#include <ATen/ops/adjoint_ops.h>
#include <ATen/ops/alias_ops.h>
#include <ATen/ops/align_as_ops.h>
#include <ATen/ops/align_to_ops.h>
#include <ATen/ops/all_ops.h>
#include <ATen/ops/allclose_ops.h>
#include <ATen/ops/amax_ops.h>
#include <ATen/ops/amin_ops.h>
#include <ATen/ops/aminmax_ops.h>
#include <ATen/ops/and_ops.h>
#include <ATen/ops/angle_ops.h>
#include <ATen/ops/any_ops.h>
#include <ATen/ops/arccos_ops.h>
#include <ATen/ops/arccosh_ops.h>
#include <ATen/ops/arcsin_ops.h>
#include <ATen/ops/arcsinh_ops.h>
#include <ATen/ops/arctan2_ops.h>
#include <ATen/ops/arctan_ops.h>
#include <ATen/ops/arctanh_ops.h>
#include <ATen/ops/argmax_ops.h>
#include <ATen/ops/argmin_ops.h>
#include <ATen/ops/argsort_ops.h>
#include <ATen/ops/argwhere_ops.h>
#include <ATen/ops/as_strided_ops.h>
#include <ATen/ops/as_strided_scatter_ops.h>
#include <ATen/ops/asin_ops.h>
#include <ATen/ops/asinh_ops.h>
#include <ATen/ops/atan2_ops.h>
#include <ATen/ops/atan_ops.h>
#include <ATen/ops/atanh_ops.h>
#include <ATen/ops/baddbmm_ops.h>
#include <ATen/ops/bernoulli_ops.h>
#include <ATen/ops/bincount_ops.h>
#include <ATen/ops/bitwise_and_ops.h>
#include <ATen/ops/bitwise_left_shift_ops.h>
#include <ATen/ops/bitwise_not_ops.h>
#include <ATen/ops/bitwise_or_ops.h>
#include <ATen/ops/bitwise_right_shift_ops.h>
#include <ATen/ops/bitwise_xor_ops.h>
#include <ATen/ops/bmm_ops.h>
#include <ATen/ops/broadcast_to_ops.h>
#include <ATen/ops/cauchy_ops.h>
#include <ATen/ops/ccol_indices_ops.h>
#include <ATen/ops/ceil_ops.h>
#include <ATen/ops/chalf_ops.h>
#include <ATen/ops/cholesky_inverse_ops.h>
#include <ATen/ops/cholesky_ops.h>
#include <ATen/ops/cholesky_solve_ops.h>
#include <ATen/ops/chunk_ops.h>
#include <ATen/ops/clamp_max_ops.h>
#include <ATen/ops/clamp_min_ops.h>
#include <ATen/ops/clamp_ops.h>
#include <ATen/ops/clip_ops.h>
#include <ATen/ops/clone_ops.h>
#include <ATen/ops/coalesce_ops.h>
#include <ATen/ops/col_indices_ops.h>
#include <ATen/ops/conj_ops.h>
#include <ATen/ops/conj_physical_ops.h>
#include <ATen/ops/contiguous_ops.h>
#include <ATen/ops/copy_ops.h>
#include <ATen/ops/copysign_ops.h>
#include <ATen/ops/corrcoef_ops.h>
#include <ATen/ops/cos_ops.h>
#include <ATen/ops/cosh_ops.h>
#include <ATen/ops/count_nonzero_ops.h>
#include <ATen/ops/cov_ops.h>
#include <ATen/ops/cross_ops.h>
#include <ATen/ops/crow_indices_ops.h>
#include <ATen/ops/cummax_ops.h>
#include <ATen/ops/cummin_ops.h>
#include <ATen/ops/cumprod_ops.h>
#include <ATen/ops/cumsum_ops.h>
#include <ATen/ops/data_ops.h>
#include <ATen/ops/deg2rad_ops.h>
#include <ATen/ops/dense_dim_ops.h>
#include <ATen/ops/dequantize_ops.h>
#include <ATen/ops/det_ops.h>
#include <ATen/ops/detach_ops.h>
#include <ATen/ops/diag_embed_ops.h>
#include <ATen/ops/diag_ops.h>
#include <ATen/ops/diagflat_ops.h>
#include <ATen/ops/diagonal_ops.h>
#include <ATen/ops/diagonal_scatter_ops.h>
#include <ATen/ops/diff_ops.h>
#include <ATen/ops/digamma_ops.h>
#include <ATen/ops/dist_ops.h>
#include <ATen/ops/div_ops.h>
#include <ATen/ops/divide_ops.h>
#include <ATen/ops/dot_ops.h>
#include <ATen/ops/dsplit_ops.h>
#include <ATen/ops/eq_ops.h>
#include <ATen/ops/equal_ops.h>
#include <ATen/ops/erf_ops.h>
#include <ATen/ops/erfc_ops.h>
#include <ATen/ops/erfinv_ops.h>
#include <ATen/ops/exp2_ops.h>
#include <ATen/ops/exp_ops.h>
#include <ATen/ops/expand_as_ops.h>
#include <ATen/ops/expand_ops.h>
#include <ATen/ops/expm1_ops.h>
#include <ATen/ops/exponential_ops.h>
#include <ATen/ops/fill_diagonal_ops.h>
#include <ATen/ops/fill_ops.h>
#include <ATen/ops/fix_ops.h>
#include <ATen/ops/flatten_ops.h>
#include <ATen/ops/flip_ops.h>
#include <ATen/ops/fliplr_ops.h>
#include <ATen/ops/flipud_ops.h>
#include <ATen/ops/float_power_ops.h>
#include <ATen/ops/floor_divide_ops.h>
#include <ATen/ops/floor_ops.h>
#include <ATen/ops/fmax_ops.h>
#include <ATen/ops/fmin_ops.h>
#include <ATen/ops/fmod_ops.h>
#include <ATen/ops/frac_ops.h>
#include <ATen/ops/frexp_ops.h>
#include <ATen/ops/gather_ops.h>
#include <ATen/ops/gcd_ops.h>
#include <ATen/ops/ge_ops.h>
#include <ATen/ops/geometric_ops.h>
#include <ATen/ops/geqrf_ops.h>
#include <ATen/ops/ger_ops.h>
#include <ATen/ops/greater_equal_ops.h>
#include <ATen/ops/greater_ops.h>
#include <ATen/ops/gt_ops.h>
#include <ATen/ops/hardshrink_backward_ops.h>
#include <ATen/ops/hardshrink_ops.h>
#include <ATen/ops/heaviside_ops.h>
#include <ATen/ops/histc_ops.h>
#include <ATen/ops/histogram_ops.h>
#include <ATen/ops/hsplit_ops.h>
#include <ATen/ops/hypot_ops.h>
#include <ATen/ops/i0_ops.h>
#include <ATen/ops/igamma_ops.h>
#include <ATen/ops/igammac_ops.h>
#include <ATen/ops/index_add_ops.h>
#include <ATen/ops/index_copy_ops.h>
#include <ATen/ops/index_fill_ops.h>
#include <ATen/ops/index_ops.h>
#include <ATen/ops/index_put_ops.h>
#include <ATen/ops/index_reduce_ops.h>
#include <ATen/ops/index_select_ops.h>
#include <ATen/ops/indices_ops.h>
#include <ATen/ops/inner_ops.h>
#include <ATen/ops/int_repr_ops.h>
#include <ATen/ops/inverse_ops.h>
#include <ATen/ops/is_coalesced_ops.h>
#include <ATen/ops/is_complex_ops.h>
#include <ATen/ops/is_conj_ops.h>
#include <ATen/ops/is_distributed_ops.h>
#include <ATen/ops/is_floating_point_ops.h>
#include <ATen/ops/is_inference_ops.h>
#include <ATen/ops/is_leaf_ops.h>
#include <ATen/ops/is_neg_ops.h>
#include <ATen/ops/is_nonzero_ops.h>
#include <ATen/ops/is_pinned_ops.h>
#include <ATen/ops/is_same_size_ops.h>
#include <ATen/ops/is_set_to_ops.h>
#include <ATen/ops/is_signed_ops.h>
#include <ATen/ops/isclose_ops.h>
#include <ATen/ops/isfinite_ops.h>
#include <ATen/ops/isinf_ops.h>
#include <ATen/ops/isnan_ops.h>
#include <ATen/ops/isneginf_ops.h>
#include <ATen/ops/isposinf_ops.h>
#include <ATen/ops/isreal_ops.h>
#include <ATen/ops/istft_ops.h>
#include <ATen/ops/item_ops.h>
#include <ATen/ops/kron_ops.h>
#include <ATen/ops/kthvalue_ops.h>
#include <ATen/ops/lcm_ops.h>
#include <ATen/ops/ldexp_ops.h>
#include <ATen/ops/le_ops.h>
#include <ATen/ops/lerp_ops.h>
#include <ATen/ops/less_equal_ops.h>
#include <ATen/ops/less_ops.h>
#include <ATen/ops/lgamma_ops.h>
#include <ATen/ops/log10_ops.h>
#include <ATen/ops/log1p_ops.h>
#include <ATen/ops/log2_ops.h>
#include <ATen/ops/log_normal_ops.h>
#include <ATen/ops/log_ops.h>
#include <ATen/ops/log_softmax_ops.h>
#include <ATen/ops/logaddexp2_ops.h>
#include <ATen/ops/logaddexp_ops.h>
#include <ATen/ops/logcumsumexp_ops.h>
#include <ATen/ops/logdet_ops.h>
#include <ATen/ops/logical_and_ops.h>
#include <ATen/ops/logical_not_ops.h>
#include <ATen/ops/logical_or_ops.h>
#include <ATen/ops/logical_xor_ops.h>
#include <ATen/ops/logit_ops.h>
#include <ATen/ops/logsumexp_ops.h>
#include <ATen/ops/lshift_ops.h>
#include <ATen/ops/lt_ops.h>
#include <ATen/ops/lu_solve_ops.h>
#include <ATen/ops/mH_ops.h>
#include <ATen/ops/mT_ops.h>
#include <ATen/ops/masked_fill_ops.h>
#include <ATen/ops/masked_scatter_ops.h>
#include <ATen/ops/masked_select_ops.h>
#include <ATen/ops/matmul_ops.h>
#include <ATen/ops/matrix_H_ops.h>
#include <ATen/ops/matrix_exp_ops.h>
#include <ATen/ops/matrix_power_ops.h>
#include <ATen/ops/max_ops.h>
#include <ATen/ops/maximum_ops.h>
#include <ATen/ops/mean_ops.h>
#include <ATen/ops/median_ops.h>
#include <ATen/ops/min_ops.h>
#include <ATen/ops/minimum_ops.h>
#include <ATen/ops/mm_ops.h>
#include <ATen/ops/mode_ops.h>
#include <ATen/ops/moveaxis_ops.h>
#include <ATen/ops/movedim_ops.h>
#include <ATen/ops/msort_ops.h>
#include <ATen/ops/mul_ops.h>
#include <ATen/ops/multinomial_ops.h>
#include <ATen/ops/multiply_ops.h>
#include <ATen/ops/mv_ops.h>
#include <ATen/ops/mvlgamma_ops.h>
#include <ATen/ops/nan_to_num_ops.h>
#include <ATen/ops/nanmean_ops.h>
#include <ATen/ops/nanmedian_ops.h>
#include <ATen/ops/nanquantile_ops.h>
#include <ATen/ops/nansum_ops.h>
#include <ATen/ops/narrow_copy_ops.h>
#include <ATen/ops/narrow_ops.h>
#include <ATen/ops/ne_ops.h>
#include <ATen/ops/neg_ops.h>
#include <ATen/ops/negative_ops.h>
#include <ATen/ops/new_empty_ops.h>
#include <ATen/ops/new_empty_strided_ops.h>
#include <ATen/ops/new_full_ops.h>
#include <ATen/ops/new_ones_ops.h>
#include <ATen/ops/new_zeros_ops.h>
#include <ATen/ops/nextafter_ops.h>
#include <ATen/ops/nonzero_numpy_ops.h>
#include <ATen/ops/nonzero_ops.h>
#include <ATen/ops/nonzero_static_ops.h>
#include <ATen/ops/norm_ops.h>
#include <ATen/ops/normal_ops.h>
#include <ATen/ops/not_equal_ops.h>
#include <ATen/ops/numpy_T_ops.h>
#include <ATen/ops/or_ops.h>
#include <ATen/ops/orgqr_ops.h>
#include <ATen/ops/ormqr_ops.h>
#include <ATen/ops/outer_ops.h>
#include <ATen/ops/output_nr_ops.h>
#include <ATen/ops/permute_ops.h>
#include <ATen/ops/pin_memory_ops.h>
#include <ATen/ops/pinverse_ops.h>
#include <ATen/ops/polygamma_ops.h>
#include <ATen/ops/positive_ops.h>
#include <ATen/ops/pow_ops.h>
#include <ATen/ops/prelu_ops.h>
#include <ATen/ops/prod_ops.h>
#include <ATen/ops/put_ops.h>
#include <ATen/ops/q_per_channel_axis_ops.h>
#include <ATen/ops/q_per_channel_scales_ops.h>
#include <ATen/ops/q_per_channel_zero_points_ops.h>
#include <ATen/ops/q_scale_ops.h>
#include <ATen/ops/q_zero_point_ops.h>
#include <ATen/ops/qr_ops.h>
#include <ATen/ops/qscheme_ops.h>
#include <ATen/ops/quantile_ops.h>
#include <ATen/ops/rad2deg_ops.h>
#include <ATen/ops/random_ops.h>
#include <ATen/ops/ravel_ops.h>
#include <ATen/ops/reciprocal_ops.h>
#include <ATen/ops/record_stream_ops.h>
#include <ATen/ops/refine_names_ops.h>
#include <ATen/ops/relu_ops.h>
#include <ATen/ops/remainder_ops.h>
#include <ATen/ops/rename_ops.h>
#include <ATen/ops/renorm_ops.h>
#include <ATen/ops/repeat_interleave_ops.h>
#include <ATen/ops/repeat_ops.h>
#include <ATen/ops/requires_grad_ops.h>
#include <ATen/ops/reshape_as_ops.h>
#include <ATen/ops/reshape_ops.h>
#include <ATen/ops/resize_as_ops.h>
#include <ATen/ops/resize_as_sparse_ops.h>
#include <ATen/ops/resize_ops.h>
#include <ATen/ops/resolve_conj_ops.h>
#include <ATen/ops/resolve_neg_ops.h>
#include <ATen/ops/retain_grad_ops.h>
#include <ATen/ops/retains_grad_ops.h>
#include <ATen/ops/roll_ops.h>
#include <ATen/ops/rot90_ops.h>
#include <ATen/ops/round_ops.h>
#include <ATen/ops/row_indices_ops.h>
#include <ATen/ops/rshift_ops.h>
#include <ATen/ops/rsqrt_ops.h>
#include <ATen/ops/scatter_add_ops.h>
#include <ATen/ops/scatter_ops.h>
#include <ATen/ops/scatter_reduce_ops.h>
#include <ATen/ops/select_ops.h>
#include <ATen/ops/select_scatter_ops.h>
#include <ATen/ops/set_data_ops.h>
#include <ATen/ops/set_ops.h>
#include <ATen/ops/sgn_ops.h>
#include <ATen/ops/sigmoid_ops.h>
#include <ATen/ops/sign_ops.h>
#include <ATen/ops/signbit_ops.h>
#include <ATen/ops/sin_ops.h>
#include <ATen/ops/sinc_ops.h>
#include <ATen/ops/sinh_ops.h>
#include <ATen/ops/size_ops.h>
#include <ATen/ops/slice_inverse_ops.h>
#include <ATen/ops/slice_ops.h>
#include <ATen/ops/slice_scatter_ops.h>
#include <ATen/ops/slogdet_ops.h>
#include <ATen/ops/smm_ops.h>
#include <ATen/ops/softmax_ops.h>
#include <ATen/ops/sort_ops.h>
#include <ATen/ops/sparse_dim_ops.h>
#include <ATen/ops/sparse_mask_ops.h>
#include <ATen/ops/sparse_resize_and_clear_ops.h>
#include <ATen/ops/sparse_resize_ops.h>
#include <ATen/ops/split_ops.h>
#include <ATen/ops/split_with_sizes_ops.h>
#include <ATen/ops/sqrt_ops.h>
#include <ATen/ops/square_ops.h>
#include <ATen/ops/squeeze_ops.h>
#include <ATen/ops/sspaddmm_ops.h>
#include <ATen/ops/std_ops.h>
#include <ATen/ops/stft_ops.h>
#include <ATen/ops/stride_ops.h>
#include <ATen/ops/sub_ops.h>
#include <ATen/ops/subtract_ops.h>
#include <ATen/ops/sum_ops.h>
#include <ATen/ops/sum_to_size_ops.h>
#include <ATen/ops/svd_ops.h>
#include <ATen/ops/swapaxes_ops.h>
#include <ATen/ops/swapdims_ops.h>
#include <ATen/ops/t_ops.h>
#include <ATen/ops/take_along_dim_ops.h>
#include <ATen/ops/take_ops.h>
#include <ATen/ops/tan_ops.h>
#include <ATen/ops/tanh_ops.h>
#include <ATen/ops/tensor_split_ops.h>
#include <ATen/ops/tile_ops.h>
#include <ATen/ops/to_dense_ops.h>
#include <ATen/ops/to_mkldnn_ops.h>
#include <ATen/ops/to_ops.h>
#include <ATen/ops/to_padded_tensor_ops.h>
#include <ATen/ops/to_sparse_bsc_ops.h>
#include <ATen/ops/to_sparse_bsr_ops.h>
#include <ATen/ops/to_sparse_csc_ops.h>
#include <ATen/ops/to_sparse_csr_ops.h>
#include <ATen/ops/to_sparse_ops.h>
#include <ATen/ops/topk_ops.h>
#include <ATen/ops/trace_ops.h>
#include <ATen/ops/transpose_ops.h>
#include <ATen/ops/triangular_solve_ops.h>
#include <ATen/ops/tril_ops.h>
#include <ATen/ops/triu_ops.h>
#include <ATen/ops/true_divide_ops.h>
#include <ATen/ops/trunc_ops.h>
#include <ATen/ops/type_as_ops.h>
#include <ATen/ops/unbind_ops.h>
#include <ATen/ops/unflatten_ops.h>
#include <ATen/ops/unfold_ops.h>
#include <ATen/ops/uniform_ops.h>
#include <ATen/ops/unsafe_chunk_ops.h>
#include <ATen/ops/unsafe_split_ops.h>
#include <ATen/ops/unsafe_split_with_sizes_ops.h>
#include <ATen/ops/unsqueeze_ops.h>
#include <ATen/ops/values_ops.h>
#include <ATen/ops/var_ops.h>
#include <ATen/ops/vdot_ops.h>
#include <ATen/ops/view_as_ops.h>
#include <ATen/ops/view_ops.h>
#include <ATen/ops/vsplit_ops.h>
#include <ATen/ops/where_ops.h>
#include <ATen/ops/xlogy_ops.h>
#include <ATen/ops/xor_ops.h>
#include <ATen/ops/zero_ops.h>

namespace at {
namespace _ops {

} // namespace _ops
} // namespace at
