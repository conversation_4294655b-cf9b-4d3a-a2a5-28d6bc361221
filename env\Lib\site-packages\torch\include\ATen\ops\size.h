#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/size_ops.h>

namespace at {


// aten::size.int(Tensor self, int dim) -> int
inline int64_t __dispatch_size(const at::Tensor & self, int64_t dim) {
    return at::_ops::size_int::call(self, dim);
}

// aten::size.Dimname(Tensor self, Dimname dim) -> int
inline int64_t size(const at::Tensor & self, at::Dimname dim) {
    return at::_ops::size_Dimname::call(self, dim);
}

}
