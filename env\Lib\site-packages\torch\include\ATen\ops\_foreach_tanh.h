#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_foreach_tanh_ops.h>

namespace at {


// aten::_foreach_tanh(Tensor[] self) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_tanh(at::TensorList self) {
    return at::_ops::_foreach_tanh::call(self);
}

// aten::_foreach_tanh_(Tensor(a!)[] self) -> ()
inline void _foreach_tanh_(at::TensorList self) {
    return at::_ops::_foreach_tanh_::call(self);
}

// aten::_foreach_tanh.out(Tensor[] self, *, Tensor(a!)[] out) -> ()
inline void _foreach_tanh_out(at::TensorList out, at::TensorList self) {
    return at::_ops::_foreach_tanh_out::call(self, out);
}
// aten::_foreach_tanh.out(Tensor[] self, *, Tensor(a!)[] out) -> ()
inline void _foreach_tanh_outf(at::TensorList self, at::TensorList out) {
    return at::_ops::_foreach_tanh_out::call(self, out);
}

}
