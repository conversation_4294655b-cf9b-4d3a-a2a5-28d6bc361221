"""
The Zen of SymPy.
"""

s = """The Zen of SymPy

Unevaluated is better than evaluated.
The user interface matters.
Printing matters.
Pure Python can be fast enough.
If it's too slow, it's (probably) your fault.
Documentation matters.
Correctness is more important than speed.
Push it in now and improve upon it later.
Coverage by testing matters.
Smart tests are better than random tests.
But random tests sometimes find what your smartest test missed.
The Python way is probably the right way.
Community is more important than code."""

print(s)
