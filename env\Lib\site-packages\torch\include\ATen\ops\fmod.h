#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/fmod_ops.h>

namespace at {


// aten::fmod.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fmod_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::fmod_Scalar_out::call(self, other, out);
}
// aten::fmod.Scalar_out(Tensor self, <PERSON>ala<PERSON> other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fmod_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out) {
    return at::_ops::fmod_Scalar_out::call(self, other, out);
}

// aten::fmod.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor fmod(const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::fmod_Scalar::call(self, other);
}

// aten::fmod.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fmod_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::fmod_Tensor_out::call(self, other, out);
}
// aten::fmod.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fmod_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::fmod_Tensor_out::call(self, other, out);
}

// aten::fmod.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor fmod(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::fmod_Tensor::call(self, other);
}

}
