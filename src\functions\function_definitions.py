"""
Additional function definitions for the function registry.
"""

from .function_registry import FunctionDefinition, FunctionParameter, FunctionCategory


def get_communication_functions():
    """Get communication-related function definitions."""
    return [
        FunctionDefinition(
            name="send_email",
            description="Send email message",
            category=FunctionCategory.COMMUNICATION,
            parameters=[
                FunctionParameter("to", "str", "Recipient email address"),
                FunctionParameter("subject", "str", "Email subject"),
                FunctionParameter("body", "str", "Email body content"),
                FunctionParameter("from_email", "str", "Sender email address", False),
            ],
            returns="bool",
            examples=["Send report summary", "Notify team members", "Send alerts"]
        ),
        FunctionDefinition(
            name="send_sms",
            description="Send SMS text message",
            category=FunctionCategory.COMMUNICATION,
            parameters=[
                FunctionParameter("to", "str", "Recipient phone number"),
                FunctionParameter("message", "str", "SMS message content"),
                FunctionParameter("from_number", "str", "Sender phone number", False),
            ],
            returns="bool",
            examples=["Send notifications", "Alert messages", "Status updates"]
        ),
        FunctionDefinition(
            name="make_api_call",
            description="Make HTTP API call",
            category=FunctionCategory.COMMUNICATION,
            parameters=[
                FunctionParameter("url", "str", "API endpoint URL"),
                FunctionParameter("method", "str", "HTTP method (GET, POST, etc.)"),
                FunctionParameter("data", "dict", "Request data", False),
                FunctionParameter("headers", "dict", "Request headers", False),
            ],
            returns="dict",
            examples=["Call external API", "Send webhook", "Fetch data from service"]
        ),
        FunctionDefinition(
            name="post_to_slack",
            description="Post message to Slack channel",
            category=FunctionCategory.COMMUNICATION,
            parameters=[
                FunctionParameter("channel", "str", "Slack channel name"),
                FunctionParameter("message", "str", "Message content"),
                FunctionParameter("webhook_url", "str", "Slack webhook URL"),
            ],
            returns="bool",
            examples=["Team notifications", "Status updates", "Alert messages"]
        ),
    ]


def get_database_functions():
    """Get database-related function definitions."""
    return [
        FunctionDefinition(
            name="execute_query",
            description="Execute SQL query on database",
            category=FunctionCategory.DATABASE,
            parameters=[
                FunctionParameter("query", "str", "SQL query to execute"),
                FunctionParameter("database", "str", "Database connection string"),
                FunctionParameter("parameters", "dict", "Query parameters", False),
            ],
            returns="list",
            examples=["Get invoices for March", "Count active users", "Update customer data"]
        ),
        FunctionDefinition(
            name="insert_record",
            description="Insert new record into database table",
            category=FunctionCategory.DATABASE,
            parameters=[
                FunctionParameter("table", "str", "Table name"),
                FunctionParameter("data", "dict", "Record data"),
                FunctionParameter("database", "str", "Database connection string"),
            ],
            returns="int",
            examples=["Add new customer", "Insert order record", "Create user account"]
        ),
        FunctionDefinition(
            name="update_record",
            description="Update existing database record",
            category=FunctionCategory.DATABASE,
            parameters=[
                FunctionParameter("table", "str", "Table name"),
                FunctionParameter("data", "dict", "Updated data"),
                FunctionParameter("where_clause", "str", "WHERE condition"),
                FunctionParameter("database", "str", "Database connection string"),
            ],
            returns="int",
            examples=["Update customer info", "Modify order status", "Change user preferences"]
        ),
        FunctionDefinition(
            name="backup_database",
            description="Create database backup",
            category=FunctionCategory.DATABASE,
            parameters=[
                FunctionParameter("database", "str", "Database connection string"),
                FunctionParameter("backup_path", "str", "Backup file path"),
            ],
            returns="bool",
            examples=["Daily database backup", "Pre-migration backup", "Emergency backup"]
        ),
    ]


def get_web_scraping_functions():
    """Get web scraping function definitions."""
    return [
        FunctionDefinition(
            name="scrape_webpage",
            description="Extract data from webpage",
            category=FunctionCategory.WEB_SCRAPING,
            parameters=[
                FunctionParameter("url", "str", "Webpage URL"),
                FunctionParameter("selectors", "dict", "CSS selectors for data extraction"),
                FunctionParameter("wait_time", "int", "Wait time in seconds", False, 0),
            ],
            returns="dict",
            examples=["Scrape product prices", "Extract news articles", "Get contact information"]
        ),
        FunctionDefinition(
            name="download_file",
            description="Download file from URL",
            category=FunctionCategory.WEB_SCRAPING,
            parameters=[
                FunctionParameter("url", "str", "File URL"),
                FunctionParameter("local_path", "str", "Local save path"),
                FunctionParameter("headers", "dict", "Request headers", False),
            ],
            returns="bool",
            examples=["Download reports", "Save images", "Get data files"]
        ),
        FunctionDefinition(
            name="monitor_webpage",
            description="Monitor webpage for changes",
            category=FunctionCategory.WEB_SCRAPING,
            parameters=[
                FunctionParameter("url", "str", "Webpage URL"),
                FunctionParameter("selector", "str", "Element to monitor"),
                FunctionParameter("interval", "int", "Check interval in minutes"),
            ],
            returns="bool",
            examples=["Monitor price changes", "Track stock availability", "Watch for updates"]
        ),
    ]


def get_analytics_functions():
    """Get analytics function definitions."""
    return [
        FunctionDefinition(
            name="calculate_statistics",
            description="Calculate statistical measures for dataset",
            category=FunctionCategory.ANALYTICS,
            parameters=[
                FunctionParameter("data", "list", "Input dataset"),
                FunctionParameter("measures", "list", "Statistical measures to calculate"),
            ],
            returns="dict",
            examples=["Calculate sales statistics", "Analyze user behavior", "Performance metrics"]
        ),
        FunctionDefinition(
            name="generate_report",
            description="Generate analytical report",
            category=FunctionCategory.ANALYTICS,
            parameters=[
                FunctionParameter("data", "list", "Input data"),
                FunctionParameter("template", "str", "Report template"),
                FunctionParameter("format", "str", "Output format (pdf, html, csv)", False, "pdf"),
            ],
            returns="str",
            examples=["Monthly sales report", "User activity report", "Financial summary"]
        ),
        FunctionDefinition(
            name="create_visualization",
            description="Create data visualization",
            category=FunctionCategory.ANALYTICS,
            parameters=[
                FunctionParameter("data", "list", "Input data"),
                FunctionParameter("chart_type", "str", "Type of chart (bar, line, pie, etc.)"),
                FunctionParameter("title", "str", "Chart title"),
                FunctionParameter("save_path", "str", "Path to save chart", False),
            ],
            returns="str",
            examples=["Sales trend chart", "User distribution pie chart", "Performance dashboard"]
        ),
        FunctionDefinition(
            name="predict_values",
            description="Make predictions using machine learning model",
            category=FunctionCategory.ANALYTICS,
            parameters=[
                FunctionParameter("model_path", "str", "Path to trained model"),
                FunctionParameter("input_data", "list", "Input features"),
                FunctionParameter("confidence_threshold", "float", "Confidence threshold", False, 0.8),
            ],
            returns="dict",
            examples=["Predict sales", "Forecast demand", "Risk assessment"]
        ),
    ]
