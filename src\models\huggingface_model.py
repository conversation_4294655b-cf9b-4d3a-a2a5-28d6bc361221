"""
Hugging Face Model Implementation - Concrete implementation using Hugging Face Transformers.
"""

import json
import re
from typing import List, Dict, Any, Optional
import logging

from .model_interface import ModelInterface, ModelResponse, FunctionCall, ExecutionPlan, ModelConfig

try:
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("Transformers library not available. Install with: pip install transformers torch")


class HuggingFaceModel(ModelInterface):
    """Hugging Face model implementation."""
    
    def __init__(self, config: ModelConfig):
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("Transformers library is required for HuggingFaceModel")
        
        self.config = config
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self._load_model()
    
    def _load_model(self):
        """Load the model and tokenizer."""
        try:
            logging.info(f"Loading model: {self.config.model_name}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                trust_remote_code=True
            )
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Determine device
            if self.config.device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = self.config.device
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                device_map="auto" if device == "cuda" else None,
                trust_remote_code=True
            )
            
            # Create pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if device == "cuda" else -1,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32
            )
            
            logging.info(f"Model loaded successfully on {device}")
            
        except Exception as e:
            logging.error(f"Failed to load model: {e}")
            raise
    
    def generate_response(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate response from the model."""
        if not self.is_available():
            raise RuntimeError("Model is not available")
        
        try:
            # Merge kwargs with config defaults
            generation_params = {
                "max_new_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "do_sample": True,
                "pad_token_id": self.tokenizer.eos_token_id,
                "return_full_text": False
            }
            
            # Generate response
            outputs = self.pipeline(prompt, **generation_params)
            
            if outputs and len(outputs) > 0:
                generated_text = outputs[0]["generated_text"]
                
                # Calculate confidence (simplified)
                confidence = min(1.0, len(generated_text) / 100)  # Basic heuristic
                
                return ModelResponse(
                    text=generated_text,
                    confidence=confidence,
                    metadata={"model": self.config.model_name}
                )
            else:
                return ModelResponse(
                    text="",
                    confidence=0.0,
                    metadata={"error": "No output generated"}
                )
                
        except Exception as e:
            logging.error(f"Error generating response: {e}")
            return ModelResponse(
                text="",
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def extract_function_calls(self, query: str, available_functions: List[str]) -> List[FunctionCall]:
        """Extract function calls from natural language query."""
        # Create prompt for function extraction
        functions_list = "\n".join([f"- {func}" for func in available_functions])
        
        prompt = f"""
You are an AI assistant that extracts function calls from natural language queries.

Available functions:
{functions_list}

Query: "{query}"

Analyze the query and identify which functions should be called to fulfill the request.
For each function, provide the function name and estimated parameters.

Respond in JSON format:
{{
    "function_calls": [
        {{
            "function_name": "function_name",
            "parameters": {{"param1": "value1", "param2": "value2"}},
            "confidence": 0.9,
            "reasoning": "explanation of why this function is needed"
        }}
    ]
}}
"""
        
        response = self.generate_response(prompt)
        return self._parse_function_calls(response.text)
    
    def create_execution_plan(self, query: str, function_calls: List[FunctionCall]) -> ExecutionPlan:
        """Create execution plan with proper sequencing and dependencies."""
        if not function_calls:
            return ExecutionPlan(
                function_calls=[],
                dependencies={},
                reasoning="No function calls identified",
                confidence=0.0
            )
        
        # Create prompt for execution planning
        calls_text = "\n".join([
            f"- {call.function_name}({call.parameters}) - {call.reasoning}"
            for call in function_calls
        ])
        
        prompt = f"""
You are an AI assistant that creates execution plans for function calls.

Query: "{query}"

Identified function calls:
{calls_text}

Create an execution plan that:
1. Orders the functions logically
2. Identifies dependencies between functions
3. Maps outputs to inputs where needed

Respond in JSON format:
{{
    "execution_order": ["function1", "function2", "function3"],
    "dependencies": {{
        "function2": ["function1"],
        "function3": ["function1", "function2"]
    }},
    "reasoning": "explanation of the execution plan",
    "confidence": 0.9
}}
"""
        
        response = self.generate_response(prompt)
        return self._parse_execution_plan(response.text, function_calls)
    
    def is_available(self) -> bool:
        """Check if model is available and ready."""
        return self.model is not None and self.tokenizer is not None
    
    def _parse_function_calls(self, response_text: str) -> List[FunctionCall]:
        """Parse function calls from model response."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                return []
            
            data = json.loads(json_match.group())
            function_calls = []
            
            for call_data in data.get("function_calls", []):
                function_call = FunctionCall(
                    function_name=call_data.get("function_name", ""),
                    parameters=call_data.get("parameters", {}),
                    confidence=call_data.get("confidence", 0.5),
                    reasoning=call_data.get("reasoning", "")
                )
                function_calls.append(function_call)
            
            return function_calls
            
        except Exception as e:
            logging.error(f"Error parsing function calls: {e}")
            return []
    
    def _parse_execution_plan(self, response_text: str, function_calls: List[FunctionCall]) -> ExecutionPlan:
        """Parse execution plan from model response."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                return ExecutionPlan(
                    function_calls=function_calls,
                    dependencies={},
                    reasoning="Failed to parse execution plan",
                    confidence=0.0
                )
            
            data = json.loads(json_match.group())
            
            # Reorder function calls based on execution order
            execution_order = data.get("execution_order", [])
            ordered_calls = []
            
            for func_name in execution_order:
                for call in function_calls:
                    if call.function_name == func_name:
                        ordered_calls.append(call)
                        break
            
            # Add any missing calls
            for call in function_calls:
                if call not in ordered_calls:
                    ordered_calls.append(call)
            
            return ExecutionPlan(
                function_calls=ordered_calls,
                dependencies=data.get("dependencies", {}),
                reasoning=data.get("reasoning", ""),
                confidence=data.get("confidence", 0.5)
            )
            
        except Exception as e:
            logging.error(f"Error parsing execution plan: {e}")
            return ExecutionPlan(
                function_calls=function_calls,
                dependencies={},
                reasoning=f"Error parsing plan: {e}",
                confidence=0.0
            )


# Register the model
from .model_interface import ModelFactory
ModelFactory.register_model("huggingface", HuggingFaceModel)
ModelFactory.register_model("mistralai", HuggingFaceModel)
ModelFactory.register_model("meta-llama", HuggingFaceModel)
ModelFactory.register_model("microsoft", HuggingFaceModel)
ModelFactory.register_model("google", HuggingFaceModel)
