"""
Execution Engine - Handles the actual execution of function call sequences.
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from ..models.model_interface import ExecutionPlan, FunctionCall


@dataclass
class ExecutionResult:
    """Result of executing a single function."""
    function_name: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = None


@dataclass
class PlanExecutionResult:
    """Result of executing an entire plan."""
    success: bool
    results: List[ExecutionResult]
    total_time: float
    error_message: Optional[str] = None


class ExecutionEngine:
    """Engine for executing function call plans."""
    
    def __init__(self, max_workers: int = 4, timeout: float = 300.0):
        """
        Initialize the execution engine.
        
        Args:
            max_workers: Maximum number of parallel workers
            timeout: Maximum execution time for the entire plan (seconds)
        """
        self.max_workers = max_workers
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
        self.function_implementations: Dict[str, Callable] = {}
        self._register_default_implementations()
    
    def register_function(self, name: str, implementation: Callable):
        """Register a function implementation."""
        self.function_implementations[name] = implementation
        self.logger.info(f"Registered function implementation: {name}")
    
    def execute_plan(self, execution_plan: ExecutionPlan) -> PlanExecutionResult:
        """
        Execute a complete execution plan.
        
        Args:
            execution_plan: The plan to execute
            
        Returns:
            PlanExecutionResult with execution results
        """
        start_time = time.time()
        results = []
        
        try:
            self.logger.info(f"Starting execution of plan with {len(execution_plan.function_calls)} functions")
            
            # Build execution order based on dependencies
            execution_order = self._build_execution_order(execution_plan)
            
            # Execute functions in order
            context = {}  # Shared context for passing data between functions
            
            for function_call in execution_order:
                result = self._execute_function(function_call, context)
                results.append(result)
                
                # If function failed and it's critical, stop execution
                if not result.success:
                    self.logger.error(f"Function {function_call.function_name} failed: {result.error}")
                    # For now, continue execution. In production, you might want to stop on critical failures
                
                # Store result in context for next functions
                context[function_call.function_name] = result.result
            
            total_time = time.time() - start_time
            success = all(result.success for result in results)
            
            return PlanExecutionResult(
                success=success,
                results=results,
                total_time=total_time
            )
            
        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error(f"Plan execution failed: {e}")
            
            return PlanExecutionResult(
                success=False,
                results=results,
                total_time=total_time,
                error_message=str(e)
            )
    
    def _execute_function(self, function_call: FunctionCall, context: Dict[str, Any]) -> ExecutionResult:
        """Execute a single function call."""
        start_time = time.time()
        
        try:
            self.logger.debug(f"Executing function: {function_call.function_name}")
            
            # Get function implementation
            if function_call.function_name not in self.function_implementations:
                return ExecutionResult(
                    function_name=function_call.function_name,
                    success=False,
                    error=f"No implementation found for function: {function_call.function_name}",
                    execution_time=time.time() - start_time
                )
            
            implementation = self.function_implementations[function_call.function_name]
            
            # Resolve parameters (substitute context values if needed)
            resolved_params = self._resolve_parameters(function_call.parameters, context)
            
            # Execute the function
            result = implementation(**resolved_params)
            
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                function_name=function_call.function_name,
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={"confidence": function_call.confidence}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Error executing {function_call.function_name}: {e}")
            
            return ExecutionResult(
                function_name=function_call.function_name,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def _build_execution_order(self, execution_plan: ExecutionPlan) -> List[FunctionCall]:
        """Build the correct execution order based on dependencies."""
        # Simple topological sort
        function_calls = {call.function_name: call for call in execution_plan.function_calls}
        dependencies = execution_plan.dependencies
        
        # Find functions with no dependencies
        no_deps = [call for call in execution_plan.function_calls 
                  if call.function_name not in dependencies or not dependencies[call.function_name]]
        
        ordered = []
        remaining = set(call.function_name for call in execution_plan.function_calls)
        
        while no_deps:
            # Take a function with no dependencies
            current = no_deps.pop(0)
            ordered.append(current)
            remaining.remove(current.function_name)
            
            # Find functions that now have no dependencies
            new_no_deps = []
            for func_name in remaining:
                deps = dependencies.get(func_name, [])
                if all(dep not in remaining for dep in deps):
                    new_no_deps.append(function_calls[func_name])
            
            # Remove from no_deps and add new ones
            no_deps.extend(new_no_deps)
        
        # Add any remaining functions (shouldn't happen with valid dependencies)
        for func_name in remaining:
            ordered.append(function_calls[func_name])
        
        return ordered
    
    def _resolve_parameters(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve parameter values, substituting context values where needed."""
        resolved = {}
        
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith("$"):
                # This is a reference to a context value
                context_key = value[1:]  # Remove the $ prefix
                if context_key in context:
                    resolved[key] = context[context_key]
                else:
                    self.logger.warning(f"Context key not found: {context_key}")
                    resolved[key] = value
            else:
                resolved[key] = value
        
        return resolved
    
    def _register_default_implementations(self):
        """Register default implementations for common functions."""
        
        # Data processing functions
        def filter_data(data: List[Dict], criteria: Dict) -> List[Dict]:
            """Filter data based on criteria."""
            if not isinstance(data, list):
                return []
            
            filtered = []
            for item in data:
                match = True
                for key, value in criteria.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    filtered.append(item)
            
            return filtered
        
        def sort_data(data: List[Dict], field: str, ascending: bool = True) -> List[Dict]:
            """Sort data by field."""
            if not isinstance(data, list):
                return []
            
            try:
                return sorted(data, key=lambda x: x.get(field, 0), reverse=not ascending)
            except Exception:
                return data
        
        def aggregate_data(data: List[Dict], operation: str, field: str = None) -> float:
            """Aggregate data using specified operation."""
            if not isinstance(data, list) or not data:
                return 0.0
            
            if operation.lower() == "count":
                return float(len(data))
            
            if not field:
                return 0.0
            
            values = [item.get(field, 0) for item in data if field in item]
            
            if operation.lower() == "sum":
                return sum(values)
            elif operation.lower() in ["avg", "average", "mean"]:
                return sum(values) / len(values) if values else 0.0
            elif operation.lower() == "max":
                return max(values) if values else 0.0
            elif operation.lower() == "min":
                return min(values) if values else 0.0
            
            return 0.0
        
        # File operations (mock implementations)
        def read_file(file_path: str, encoding: str = "utf-8") -> str:
            """Mock file reading."""
            return f"Mock content from {file_path}"
        
        def write_file(file_path: str, content: str, mode: str = "w") -> bool:
            """Mock file writing."""
            self.logger.info(f"Mock: Writing to {file_path} with mode {mode}")
            return True
        
        # Communication functions (mock implementations)
        def send_email(to: str, subject: str, body: str, cc: str = None, attachments: List = None) -> bool:
            """Mock email sending."""
            self.logger.info(f"Mock: Sending email to {to} with subject '{subject}'")
            return True
        
        def make_api_call(url: str, method: str = "GET", headers: Dict = None, data: Dict = None) -> Dict:
            """Mock API call."""
            self.logger.info(f"Mock: Making {method} request to {url}")
            return {"status": "success", "data": "mock_response"}
        
        # Register all implementations
        implementations = {
            "filter_data": filter_data,
            "sort_data": sort_data,
            "aggregate_data": aggregate_data,
            "read_file": read_file,
            "write_file": write_file,
            "send_email": send_email,
            "make_api_call": make_api_call,
        }
        
        for name, impl in implementations.items():
            self.register_function(name, impl)


class MockExecutionEngine(ExecutionEngine):
    """Mock execution engine for testing and demonstration."""
    
    def __init__(self):
        super().__init__()
        self.logger.info("Using mock execution engine - no actual functions will be executed")
    
    def _execute_function(self, function_call: FunctionCall, context: Dict[str, Any]) -> ExecutionResult:
        """Mock function execution."""
        start_time = time.time()
        
        # Simulate some execution time
        time.sleep(0.1)
        
        # Generate mock result based on function name
        mock_result = self._generate_mock_result(function_call.function_name, function_call.parameters)
        
        execution_time = time.time() - start_time
        
        return ExecutionResult(
            function_name=function_call.function_name,
            success=True,
            result=mock_result,
            execution_time=execution_time,
            metadata={"mock": True, "confidence": function_call.confidence}
        )
    
    def _generate_mock_result(self, function_name: str, parameters: Dict[str, Any]) -> Any:
        """Generate mock results based on function type."""
        if "aggregate" in function_name or "sum" in function_name:
            return 12345.67
        elif "filter" in function_name or "search" in function_name:
            return [{"id": 1, "name": "Mock Item 1"}, {"id": 2, "name": "Mock Item 2"}]
        elif "send" in function_name or "email" in function_name:
            return True
        elif "read" in function_name:
            return "Mock file content"
        elif "write" in function_name:
            return True
        else:
            return f"Mock result for {function_name}"
