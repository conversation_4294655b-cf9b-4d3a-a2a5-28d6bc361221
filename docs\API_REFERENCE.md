# API Reference

## Core Classes

### FunctionCallPipeline

Main pipeline class for processing natural language queries.

```python
class FunctionCallPipeline:
    def __init__(self, 
                 model_config: Optional[ModelConfig] = None,
                 function_registry: Optional[FunctionRegistry] = None,
                 enable_execution: bool = False)
```

#### Methods

- `process_query(query: str, **kwargs) -> PipelineResult`
- `get_available_functions() -> List[str]`
- `search_functions(search_term: str) -> List[str]`
- `explain_plan(execution_plan: ExecutionPlan) -> str`

### PipelineBuilder

Builder pattern for creating configured pipelines.

```python
builder = (PipelineBuilder()
          .with_model(model_config)
          .with_functions(function_registry)
          .with_execution(True)
          .build())
```

### ModelInterface

Abstract interface for AI models.

```python
class ModelInterface(ABC):
    @abstractmethod
    def generate_response(self, prompt: str, **kwargs) -> ModelResponse
    
    @abstractmethod
    def extract_function_calls(self, query: str, available_functions: List[str]) -> List[FunctionCall]
    
    @abstractmethod
    def create_execution_plan(self, query: str, function_calls: List[FunctionCall]) -> ExecutionPlan
```

### FunctionRegistry

Registry for managing available functions.

```python
class FunctionRegistry:
    def register_function(self, func_def: FunctionDefinition)
    def get_function(self, name: str) -> Optional[FunctionDefinition]
    def search_functions(self, query: str) -> List[FunctionDefinition]
    def get_all_functions() -> List[FunctionDefinition]
```

## Data Classes

### ExecutionPlan

```python
@dataclass
class ExecutionPlan:
    function_calls: List[FunctionCall]
    dependencies: Dict[str, List[str]]
    reasoning: str
    confidence: float
```

### FunctionCall

```python
@dataclass
class FunctionCall:
    function_name: str
    parameters: Dict[str, Any]
    confidence: float
    reasoning: str = ""
```

### PipelineResult

```python
@dataclass
class PipelineResult:
    query: str
    execution_plan: ExecutionPlan
    success: bool
    error_message: Optional[str] = None
    execution_results: Optional[List[Dict[str, Any]]] = None
```

## Configuration Classes

### ModelConfig

```python
class ModelConfig:
    def __init__(self, 
                 model_name: str,
                 model_path: Optional[str] = None,
                 device: str = "auto",
                 max_tokens: int = 2048,
                 temperature: float = 0.7,
                 top_p: float = 0.9,
                 **kwargs)
```

### ConfigLoader

```python
class ConfigLoader:
    def get_model_config(self, model_name: Optional[str] = None) -> ModelConfig
    def get_pipeline_config() -> Dict[str, Any]
    def is_execution_enabled() -> bool
    def is_mock_mode() -> bool
```

## Function Definition Classes

### FunctionDefinition

```python
@dataclass
class FunctionDefinition:
    name: str
    description: str
    category: FunctionCategory
    parameters: List[FunctionParameter]
    returns: str
    examples: List[str]
    implementation: Optional[Callable] = None
```

### FunctionParameter

```python
@dataclass
class FunctionParameter:
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
```

## Execution Engine

### ExecutionEngine

```python
class ExecutionEngine:
    def __init__(self, max_workers: int = 4, timeout: float = 300.0)
    def register_function(self, name: str, implementation: Callable)
    def execute_plan(self, execution_plan: ExecutionPlan) -> PlanExecutionResult
```

### MockExecutionEngine

Safe mock implementation for testing.

```python
class MockExecutionEngine(ExecutionEngine):
    # Provides mock implementations of all functions
    # Safe for testing and demonstration
```

## Query Parser

### QueryParser

```python
class QueryParser:
    def parse(self, query: str) -> ParsedQuery
```

### ParsedQuery

```python
@dataclass
class ParsedQuery:
    original_query: str
    query_type: QueryType
    intents: List[QueryIntent]
    entities: Dict[str, List[str]]
    keywords: List[str]
    temporal_expressions: List[str]
    confidence: float
```

## Enums

### FunctionCategory

```python
class FunctionCategory(Enum):
    DATA_PROCESSING = "data_processing"
    FILE_OPERATIONS = "file_operations"
    COMMUNICATION = "communication"
    DATABASE = "database"
    WEB_SCRAPING = "web_scraping"
    ANALYTICS = "analytics"
    AUTHENTICATION = "authentication"
    SCHEDULING = "scheduling"
    NOTIFICATION = "notification"
    CONVERSION = "conversion"
    VALIDATION = "validation"
    SEARCH = "search"
```

### QueryType

```python
class QueryType(Enum):
    RETRIEVAL = "retrieval"
    PROCESSING = "processing"
    COMMUNICATION = "communication"
    ANALYSIS = "analysis"
    AUTOMATION = "automation"
    MIXED = "mixed"
```

### QueryIntent

```python
class QueryIntent(Enum):
    GET_DATA = "get_data"
    PROCESS_DATA = "process_data"
    SEND_MESSAGE = "send_message"
    CREATE_REPORT = "create_report"
    SCHEDULE_TASK = "schedule_task"
    ANALYZE_DATA = "analyze_data"
    # ... and more
```

## Utility Functions

### Configuration

```python
from src.utils.config_loader import get_config_loader, setup_logging

config = get_config_loader()
setup_logging(config)
```

### Model Factory

```python
from src.models.model_interface import ModelFactory, DEFAULT_MODELS

# Create model from config
model = ModelFactory.create_model(model_config)

# Get available models
models = ModelFactory.get_available_models()

# Use default configurations
model_config = DEFAULT_MODELS["mistral-7b"]
```

## Error Handling

### Common Exceptions

- `ModelNotAvailableError`: Model cannot be loaded
- `FunctionNotFoundError`: Requested function doesn't exist
- `ValidationError`: Execution plan validation failed
- `ExecutionError`: Function execution failed
- `ConfigurationError`: Invalid configuration

### Error Response Format

```python
{
    "success": False,
    "error_message": "Description of the error",
    "error_type": "ErrorClassName",
    "details": {
        # Additional error context
    }
}
```

## Examples

### Basic Usage

```python
from src.pipeline.core import FunctionCallPipeline

pipeline = FunctionCallPipeline()
result = pipeline.process_query("Send me a summary of today's sales")

if result.success:
    for call in result.execution_plan.function_calls:
        print(f"{call.function_name}: {call.parameters}")
```

### Custom Function Registration

```python
from src.functions.function_registry import FunctionRegistry, FunctionDefinition

def my_function(param1: str, param2: int = 10) -> str:
    return f"Processed {param1} with {param2}"

registry = FunctionRegistry()
registry.register_function(FunctionDefinition(
    name="my_function",
    description="Custom processing function",
    category=FunctionCategory.DATA_PROCESSING,
    parameters=[
        FunctionParameter("param1", "str", "Input string"),
        FunctionParameter("param2", "int", "Processing parameter", False, 10)
    ],
    returns="str",
    examples=["Process user input"],
    implementation=my_function
))
```

### Advanced Pipeline Configuration

```python
from src.pipeline.core import PipelineBuilder
from src.models.model_interface import ModelConfig

custom_config = ModelConfig(
    model_name="custom/model",
    temperature=0.5,
    max_tokens=1024
)

pipeline = (PipelineBuilder()
           .with_model(custom_config)
           .with_execution(True)
           .build())
```
