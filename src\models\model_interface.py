"""
Model Interface - Abstract interface for AI models used in the pipeline.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelResponse:
    """Response from AI model."""
    text: str
    confidence: float
    metadata: Dict[str, Any] = None


@dataclass
class FunctionCall:
    """Represents a function call extracted from model response."""
    function_name: str
    parameters: Dict[str, Any]
    confidence: float
    reasoning: str = ""


@dataclass
class ExecutionPlan:
    """Represents a complete execution plan."""
    function_calls: List[FunctionCall]
    dependencies: Dict[str, List[str]]  # function_name -> list of dependencies
    reasoning: str
    confidence: float


class ModelInterface(ABC):
    """Abstract interface for AI models."""
    
    @abstractmethod
    def generate_response(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate response from the model."""
        pass
    
    @abstractmethod
    def extract_function_calls(self, query: str, available_functions: List[str]) -> List[FunctionCall]:
        """Extract function calls from natural language query."""
        pass
    
    @abstractmethod
    def create_execution_plan(self, query: str, function_calls: List[FunctionCall]) -> ExecutionPlan:
        """Create execution plan with proper sequencing and dependencies."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if model is available and ready."""
        pass


class ModelConfig:
    """Configuration for AI models."""
    
    def __init__(self, 
                 model_name: str,
                 model_path: Optional[str] = None,
                 device: str = "auto",
                 max_tokens: int = 2048,
                 temperature: float = 0.7,
                 top_p: float = 0.9,
                 **kwargs):
        self.model_name = model_name
        self.model_path = model_path
        self.device = device
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.top_p = top_p
        self.additional_params = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "model_name": self.model_name,
            "model_path": self.model_path,
            "device": self.device,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            **self.additional_params
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "ModelConfig":
        """Create config from dictionary."""
        return cls(**config_dict)


class ModelFactory:
    """Factory for creating model instances."""
    
    _registered_models = {}
    
    @classmethod
    def register_model(cls, name: str, model_class: type):
        """Register a model class."""
        cls._registered_models[name] = model_class
    
    @classmethod
    def create_model(cls, config: ModelConfig) -> ModelInterface:
        """Create model instance from config."""
        model_type = config.model_name.split("/")[0] if "/" in config.model_name else "huggingface"
        
        if model_type not in cls._registered_models:
            raise ValueError(f"Unknown model type: {model_type}")
        
        model_class = cls._registered_models[model_type]
        return model_class(config)
    
    @classmethod
    def get_available_models(cls) -> List[str]:
        """Get list of available model types."""
        return list(cls._registered_models.keys())


# Default model configurations
DEFAULT_MODELS = {
    "mistral-7b": ModelConfig(
        model_name="mistralai/Mistral-7B-Instruct-v0.2",
        device="auto",
        max_tokens=2048,
        temperature=0.7
    ),
    "llama2-7b": ModelConfig(
        model_name="meta-llama/Llama-2-7b-chat-hf",
        device="auto",
        max_tokens=2048,
        temperature=0.7
    ),
    "phi-3": ModelConfig(
        model_name="microsoft/Phi-3-mini-4k-instruct",
        device="auto",
        max_tokens=2048,
        temperature=0.7
    ),
    "gemma-7b": ModelConfig(
        model_name="google/gemma-7b-it",
        device="auto",
        max_tokens=2048,
        temperature=0.7
    )
}
