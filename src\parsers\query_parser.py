"""
Query Parser - Parses and analyzes natural language queries.
"""

import re
from typing import Dict, List, Any, Set
from dataclasses import dataclass
from enum import Enum


class QueryType(Enum):
    """Types of queries."""
    RETRIEVAL = "retrieval"
    PROCESSING = "processing"
    COMMUNICATION = "communication"
    ANALYSIS = "analysis"
    AUTOMATION = "automation"
    MIXED = "mixed"


class QueryIntent(Enum):
    """Intent categories for queries."""
    GET_DATA = "get_data"
    PROCESS_DATA = "process_data"
    SEND_MESSAGE = "send_message"
    CREATE_REPORT = "create_report"
    SCHEDULE_TASK = "schedule_task"
    ANALYZE_DATA = "analyze_data"
    TRANSFORM_DATA = "transform_data"
    VALIDATE_DATA = "validate_data"
    SEARCH_DATA = "search_data"
    BACKUP_DATA = "backup_data"


@dataclass
class ParsedQuery:
    """Represents a parsed query."""
    original_query: str
    query_type: QueryType
    intents: List[QueryIntent]
    entities: Dict[str, List[str]]
    keywords: List[str]
    temporal_expressions: List[str]
    confidence: float


class QueryParser:
    """Parses natural language queries to extract structure and intent."""
    
    def __init__(self):
        self.action_patterns = self._build_action_patterns()
        self.entity_patterns = self._build_entity_patterns()
        self.temporal_patterns = self._build_temporal_patterns()
    
    def parse(self, query: str) -> ParsedQuery:
        """Parse a natural language query."""
        query_lower = query.lower().strip()
        
        # Extract components
        intents = self._extract_intents(query_lower)
        entities = self._extract_entities(query)
        keywords = self._extract_keywords(query_lower)
        temporal_expressions = self._extract_temporal_expressions(query)
        query_type = self._determine_query_type(intents)
        
        # Calculate confidence based on how many components we successfully extracted
        confidence = self._calculate_confidence(intents, entities, keywords)
        
        return ParsedQuery(
            original_query=query,
            query_type=query_type,
            intents=intents,
            entities=entities,
            keywords=keywords,
            temporal_expressions=temporal_expressions,
            confidence=confidence
        )
    
    def _extract_intents(self, query: str) -> List[QueryIntent]:
        """Extract intents from the query."""
        intents = []
        
        # Check for different intent patterns
        intent_patterns = {
            QueryIntent.GET_DATA: [
                r'\b(get|retrieve|fetch|find|show|list|display)\b',
                r'\b(all|every)\s+\w+',
            ],
            QueryIntent.PROCESS_DATA: [
                r'\b(process|calculate|compute|sum|count|aggregate)\b',
                r'\b(total|average|mean|sum)\b',
            ],
            QueryIntent.SEND_MESSAGE: [
                r'\b(send|email|mail|notify|alert)\b',
                r'\bto\s+(my\s+)?(email|phone|slack)\b',
            ],
            QueryIntent.CREATE_REPORT: [
                r'\b(report|summary|generate|create)\b',
                r'\b(summarize|compile)\b',
            ],
            QueryIntent.SCHEDULE_TASK: [
                r'\b(schedule|remind|set\s+reminder)\b',
                r'\b(daily|weekly|monthly)\b',
            ],
            QueryIntent.ANALYZE_DATA: [
                r'\b(analyze|analysis|examine|study)\b',
                r'\b(trend|pattern|insight)\b',
            ],
            QueryIntent.TRANSFORM_DATA: [
                r'\b(convert|transform|format|export)\b',
                r'\b(to|into)\s+\w+\s+(format|file)\b',
            ],
            QueryIntent.VALIDATE_DATA: [
                r'\b(validate|verify|check|confirm)\b',
                r'\b(correct|valid|accurate)\b',
            ],
            QueryIntent.SEARCH_DATA: [
                r'\b(search|find|look\s+for|locate)\b',
                r'\bwhere\b.*\bis\b',
            ],
            QueryIntent.BACKUP_DATA: [
                r'\b(backup|save|store|archive)\b',
                r'\b(copy|duplicate)\b',
            ],
        }
        
        for intent, patterns in intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query):
                    if intent not in intents:
                        intents.append(intent)
                    break
        
        return intents if intents else [QueryIntent.GET_DATA]  # Default intent
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract entities from the query."""
        entities = {
            "dates": [],
            "files": [],
            "emails": [],
            "numbers": [],
            "data_types": [],
            "actions": [],
        }
        
        # Extract dates
        date_patterns = [
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b',
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
            r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',
            r'\b(today|yesterday|tomorrow|last\s+week|next\s+week|this\s+month)\b',
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            entities["dates"].extend(matches)
        
        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["emails"] = re.findall(email_pattern, query)
        
        # Extract numbers
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        entities["numbers"] = re.findall(number_pattern, query)
        
        # Extract file references
        file_patterns = [
            r'\b\w+\.(csv|json|xml|pdf|txt|xlsx|doc)\b',
            r'\b(file|document|report|spreadsheet)\b',
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            entities["files"].extend(matches)
        
        # Extract data types
        data_type_patterns = [
            r'\b(invoice|invoices|customer|customers|user|users|order|orders)\b',
            r'\b(transaction|transactions|payment|payments|record|records)\b',
            r'\b(data|information|details|statistics|metrics)\b',
        ]
        
        for pattern in data_type_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            entities["data_types"].extend(matches)
        
        # Extract action words
        action_pattern = r'\b(retrieve|send|create|generate|calculate|process|analyze|export|import|backup)\b'
        entities["actions"] = re.findall(action_pattern, query, re.IGNORECASE)
        
        return entities
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from the query."""
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]+\b', query)
        keywords = [word for word in words if word.lower() not in stop_words and len(word) > 2]
        
        return keywords
    
    def _extract_temporal_expressions(self, query: str) -> List[str]:
        """Extract temporal expressions from the query."""
        temporal_patterns = [
            r'\b(today|yesterday|tomorrow)\b',
            r'\b(this|last|next)\s+(week|month|year|quarter)\b',
            r'\b(daily|weekly|monthly|yearly|annually)\b',
            r'\b(morning|afternoon|evening|night)\b',
            r'\b\d{1,2}:\d{2}\s*(am|pm)?\b',
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(st|nd|rd|th)?\b',
        ]
        
        temporal_expressions = []
        for pattern in temporal_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            temporal_expressions.extend(matches)
        
        return temporal_expressions
    
    def _determine_query_type(self, intents: List[QueryIntent]) -> QueryType:
        """Determine the overall type of the query based on intents."""
        if len(intents) > 2:
            return QueryType.MIXED
        
        # Map intents to query types
        intent_to_type = {
            QueryIntent.GET_DATA: QueryType.RETRIEVAL,
            QueryIntent.SEARCH_DATA: QueryType.RETRIEVAL,
            QueryIntent.PROCESS_DATA: QueryType.PROCESSING,
            QueryIntent.TRANSFORM_DATA: QueryType.PROCESSING,
            QueryIntent.ANALYZE_DATA: QueryType.ANALYSIS,
            QueryIntent.CREATE_REPORT: QueryType.ANALYSIS,
            QueryIntent.SEND_MESSAGE: QueryType.COMMUNICATION,
            QueryIntent.SCHEDULE_TASK: QueryType.AUTOMATION,
            QueryIntent.VALIDATE_DATA: QueryType.PROCESSING,
            QueryIntent.BACKUP_DATA: QueryType.AUTOMATION,
        }
        
        if intents:
            primary_intent = intents[0]
            return intent_to_type.get(primary_intent, QueryType.MIXED)
        
        return QueryType.RETRIEVAL  # Default
    
    def _calculate_confidence(self, intents: List[QueryIntent], entities: Dict[str, List[str]], keywords: List[str]) -> float:
        """Calculate confidence score for the parsing."""
        score = 0.0
        
        # Intent detection contributes 40%
        if intents:
            score += 0.4
        
        # Entity extraction contributes 30%
        entity_count = sum(len(entity_list) for entity_list in entities.values())
        if entity_count > 0:
            score += min(0.3, entity_count * 0.1)
        
        # Keyword extraction contributes 30%
        if keywords:
            score += min(0.3, len(keywords) * 0.05)
        
        return min(1.0, score)
    
    def _build_action_patterns(self) -> Dict[str, str]:
        """Build patterns for action detection."""
        return {
            "retrieve": r'\b(get|retrieve|fetch|find|show|list|display)\b',
            "process": r'\b(process|calculate|compute|sum|count|aggregate)\b',
            "send": r'\b(send|email|mail|notify|alert)\b',
            "create": r'\b(create|generate|make|build|produce)\b',
            "analyze": r'\b(analyze|analysis|examine|study|investigate)\b',
        }
    
    def _build_entity_patterns(self) -> Dict[str, str]:
        """Build patterns for entity detection."""
        return {
            "date": r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
            "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "file": r'\b\w+\.(csv|json|xml|pdf|txt|xlsx|doc)\b',
            "number": r'\b\d+(?:\.\d+)?\b',
        }
    
    def _build_temporal_patterns(self) -> List[str]:
        """Build patterns for temporal expression detection."""
        return [
            r'\b(today|yesterday|tomorrow)\b',
            r'\b(this|last|next)\s+(week|month|year)\b',
            r'\b(daily|weekly|monthly|yearly)\b',
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b',
        ]
