import sys
from collections.abc import Callable, Iterator, Mapping
from typing import Any, ClassVar, Generic, TypeVar, final, overload
from typing_extensions import ParamSpec, Self

if sys.version_info >= (3, 9):
    from types import GenericAlias

_T = TypeVar("_T")
_D = TypeVar("_D")
_P = ParamSpec("_P")

@final
class ContextVar(Generic[_T]):
    @overload
    def __new__(cls, name: str) -> Self: ...
    @overload
    def __new__(cls, name: str, *, default: _T) -> Self: ...
    def __hash__(self) -> int: ...
    @property
    def name(self) -> str: ...
    @overload
    def get(self) -> _T: ...
    @overload
    def get(self, default: _T, /) -> _T: ...
    @overload
    def get(self, default: _D, /) -> _D | _T: ...
    def set(self, value: _T, /) -> Token[_T]: ...
    def reset(self, token: Token[_T], /) -> None: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

@final
class Token(Generic[_T]):
    @property
    def var(self) -> ContextVar[_T]: ...
    @property
    def old_value(self) -> Any: ...  # returns either _T or MISSING, but that's hard to express
    MISSING: ClassVar[object]
    __hash__: ClassVar[None]  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

def copy_context() -> Context: ...

# It doesn't make sense to make this generic, because for most Contexts each ContextVar will have
# a different value.
@final
class Context(Mapping[ContextVar[Any], Any]):
    def __init__(self) -> None: ...
    @overload
    def get(self, key: ContextVar[_T], default: None = None, /) -> _T | None: ...
    @overload
    def get(self, key: ContextVar[_T], default: _T, /) -> _T: ...
    @overload
    def get(self, key: ContextVar[_T], default: _D, /) -> _T | _D: ...
    def run(self, callable: Callable[_P, _T], *args: _P.args, **kwargs: _P.kwargs) -> _T: ...
    def copy(self) -> Context: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    def __getitem__(self, key: ContextVar[_T], /) -> _T: ...
    def __iter__(self) -> Iterator[ContextVar[Any]]: ...
    def __len__(self) -> int: ...
    def __eq__(self, value: object, /) -> bool: ...
