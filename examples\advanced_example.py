#!/usr/bin/env python3
"""
Advanced Example - Demonstrates advanced features of the AI Function Call Pipeline.
"""

import sys
import os
import json
from pathlib import Path

# Add the src directory to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pipeline.core import FunctionCallPipeline, PipelineBuilder
from models.model_interface import ModelConfig
from utils.config_loader import Config<PERSON>oader, setup_logging
from utils.execution_engine import MockExecutionEngine
from functions.function_registry import FunctionRegistry


def main():
    """Run advanced pipeline examples."""
    print("🚀 AI Function Call Pipeline - Advanced Example")
    print("=" * 55)
    
    # Setup
    config_loader = ConfigLoader()
    setup_logging(config_loader)
    
    # Complex real-world scenarios
    complex_queries = [
        {
            "query": "Analyze last quarter's sales data, identify top performing products, create a detailed report with visualizations, and schedule it to be sent to the management team every Monday.",
            "description": "Multi-step analytics and automation"
        },
        {
            "query": "Monitor our website for price changes on competitor products, update our pricing database, and send alerts if any competitor drops prices by more than 10%.",
            "description": "Web scraping and monitoring workflow"
        },
        {
            "query": "Process all pending customer support tickets, categorize them by urgency, assign to appropriate teams, and generate a summary dashboard.",
            "description": "Data processing and workflow automation"
        },
        {
            "query": "Extract data from multiple CSV files in the reports folder, merge them based on customer ID, validate data quality, and export to a clean master dataset.",
            "description": "ETL (Extract, Transform, Load) pipeline"
        },
        {
            "query": "Backup all critical databases, compress the files, upload to cloud storage, verify integrity, and send confirmation email with backup status.",
            "description": "Backup and verification workflow"
        }
    ]
    
    print("\n📋 Complex Scenario Examples:")
    for i, scenario in enumerate(complex_queries, 1):
        print(f"{i}. {scenario['description']}")
        print(f"   Query: {scenario['query']}")
        print()
    
    # Create advanced pipeline
    print("🔧 Setting up advanced pipeline...")
    
    try:
        # Try to create pipeline with actual model
        model_config = config_loader.get_model_config()
        pipeline = (PipelineBuilder()
                   .with_model(model_config)
                   .with_execution(False)  # Keep safe for demo
                   .build())
        
        print("✅ Advanced pipeline created successfully!")
        
    except Exception as e:
        print(f"⚠️  Model not available, using demo mode: {e}")
        pipeline = create_advanced_demo_pipeline()
    
    # Process complex scenarios
    print("\n🔍 Processing Complex Scenarios:")
    print("=" * 50)
    
    for i, scenario in enumerate(complex_queries, 1):
        print(f"\n{i}. {scenario['description']}")
        print(f"Query: {scenario['query']}")
        print("-" * 50)
        
        try:
            result = pipeline.process_query(scenario['query'])
            
            if result.success:
                plan = result.execution_plan
                print(f"✅ Analysis successful! (Confidence: {plan.confidence:.2f})")
                
                # Detailed execution plan
                print(f"\n📋 Execution Plan ({len(plan.function_calls)} steps):")
                for j, call in enumerate(plan.function_calls, 1):
                    print(f"   {j}. {call.function_name}")
                    print(f"      Parameters: {json.dumps(call.parameters, indent=8)}")
                    if call.reasoning:
                        print(f"      Reasoning: {call.reasoning}")
                    print()
                
                # Dependency analysis
                if plan.dependencies:
                    print("🔗 Dependency Graph:")
                    for func, deps in plan.dependencies.items():
                        if deps:
                            print(f"   {func} ← {', '.join(deps)}")
                
                # Execution flow visualization
                print("\n🔄 Execution Flow:")
                execution_order = get_execution_order(plan)
                for step, func_name in enumerate(execution_order, 1):
                    print(f"   Step {step}: {func_name}")
                
                # Complexity analysis
                complexity = analyze_complexity(plan)
                print(f"\n📊 Complexity Analysis:")
                print(f"   Functions: {complexity['function_count']}")
                print(f"   Dependencies: {complexity['dependency_count']}")
                print(f"   Parallel potential: {complexity['parallel_steps']}")
                print(f"   Estimated time: {complexity['estimated_time']:.1f}s")
                
            else:
                print(f"❌ Analysis failed: {result.error_message}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("=" * 50)
    
    # Demonstrate function categorization
    print("\n📚 Function Library Analysis:")
    analyze_function_library(pipeline)
    
    # Demonstrate query parsing capabilities
    print("\n🔍 Query Parsing Analysis:")
    demonstrate_query_parsing(pipeline)
    
    print("\n✨ Advanced example completed!")


def create_advanced_demo_pipeline():
    """Create an advanced demo pipeline with sophisticated planning."""
    from functions.function_registry import FunctionRegistry
    from parsers.query_parser import QueryParser
    from models.model_interface import ExecutionPlan, FunctionCall
    from pipeline.core import PipelineResult
    
    class AdvancedDemoPipeline:
        def __init__(self):
            self.function_registry = FunctionRegistry()
            self.query_parser = QueryParser()
        
        def process_query(self, query):
            # Advanced parsing
            parsed = self.query_parser.parse(query)
            
            # Create sophisticated execution plan based on query analysis
            function_calls = []
            dependencies = {}
            
            # Analyze query for different patterns
            query_lower = query.lower()
            
            # Data retrieval patterns
            if any(word in query_lower for word in ["analyze", "data", "sales", "quarter"]):
                function_calls.append(FunctionCall(
                    function_name="execute_query",
                    parameters={"query": "SELECT * FROM sales_data WHERE quarter = 'Q3'"},
                    confidence=0.9,
                    reasoning="Retrieve sales data for analysis"
                ))
                
                function_calls.append(FunctionCall(
                    function_name="aggregate_data",
                    parameters={"data": "$execute_query", "operation": "sum", "field": "revenue"},
                    confidence=0.85,
                    reasoning="Calculate total revenue"
                ))
                dependencies["aggregate_data"] = ["execute_query"]
            
            # Reporting patterns
            if any(word in query_lower for word in ["report", "visualization", "dashboard"]):
                function_calls.append(FunctionCall(
                    function_name="generate_report",
                    parameters={"data": "$aggregate_data", "template": "sales_report", "format": "pdf"},
                    confidence=0.8,
                    reasoning="Generate comprehensive sales report"
                ))
                dependencies["generate_report"] = ["aggregate_data"]
                
                function_calls.append(FunctionCall(
                    function_name="create_visualization",
                    parameters={"data": "$aggregate_data", "chart_type": "bar", "title": "Sales Performance"},
                    confidence=0.75,
                    reasoning="Create visual representation of data"
                ))
                dependencies["create_visualization"] = ["aggregate_data"]
            
            # Communication patterns
            if any(word in query_lower for word in ["send", "email", "notify", "alert"]):
                function_calls.append(FunctionCall(
                    function_name="send_email",
                    parameters={
                        "to": "<EMAIL>",
                        "subject": "Sales Report",
                        "body": "Please find the attached sales report.",
                        "attachments": ["$generate_report"]
                    },
                    confidence=0.7,
                    reasoning="Send report to management team"
                ))
                dependencies["send_email"] = ["generate_report"]
            
            # Scheduling patterns
            if any(word in query_lower for word in ["schedule", "monday", "weekly", "recurring"]):
                function_calls.append(FunctionCall(
                    function_name="schedule_task",
                    parameters={
                        "task_name": "Weekly Sales Report",
                        "schedule_time": "0 9 * * 1",  # Every Monday at 9 AM
                        "function_call": {"name": "send_email", "params": "$send_email"}
                    },
                    confidence=0.8,
                    reasoning="Schedule recurring report delivery"
                ))
                dependencies["schedule_task"] = ["send_email"]
            
            # Web scraping patterns
            if any(word in query_lower for word in ["monitor", "website", "competitor", "price"]):
                function_calls.append(FunctionCall(
                    function_name="scrape_webpage",
                    parameters={
                        "url": "https://competitor.com/products",
                        "selectors": {"price": ".price", "product": ".product-name"}
                    },
                    confidence=0.7,
                    reasoning="Monitor competitor pricing"
                ))
                
                function_calls.append(FunctionCall(
                    function_name="update_record",
                    parameters={
                        "table": "competitor_prices",
                        "data": "$scrape_webpage",
                        "where_clause": "product_id = ?"
                    },
                    confidence=0.8,
                    reasoning="Update pricing database"
                ))
                dependencies["update_record"] = ["scrape_webpage"]
            
            execution_plan = ExecutionPlan(
                function_calls=function_calls,
                dependencies=dependencies,
                reasoning=f"Advanced execution plan for: {parsed.query_type.value} query with {len(parsed.intents)} intents",
                confidence=0.8
            )
            
            return PipelineResult(
                query=query,
                execution_plan=execution_plan,
                success=True
            )
        
        def get_available_functions(self):
            return [func.name for func in self.function_registry.get_all_functions()]
        
        def search_functions(self, term):
            results = self.function_registry.search_functions(term)
            return [func.name for func in results]
    
    return AdvancedDemoPipeline()


def get_execution_order(plan):
    """Get the execution order based on dependencies."""
    # Simple topological sort
    remaining = {call.function_name for call in plan.function_calls}
    ordered = []
    
    while remaining:
        # Find functions with no unresolved dependencies
        ready = []
        for func_name in remaining:
            deps = plan.dependencies.get(func_name, [])
            if all(dep not in remaining for dep in deps):
                ready.append(func_name)
        
        if not ready:
            # Circular dependency or error, add remaining arbitrarily
            ready = list(remaining)
        
        # Add first ready function
        func_name = ready[0]
        ordered.append(func_name)
        remaining.remove(func_name)
    
    return ordered


def analyze_complexity(plan):
    """Analyze the complexity of an execution plan."""
    function_count = len(plan.function_calls)
    dependency_count = sum(len(deps) for deps in plan.dependencies.values())
    
    # Calculate potential parallel steps
    execution_order = get_execution_order(plan)
    parallel_steps = function_count - dependency_count
    
    # Estimate execution time (mock calculation)
    estimated_time = function_count * 2.5 + dependency_count * 0.5
    
    return {
        "function_count": function_count,
        "dependency_count": dependency_count,
        "parallel_steps": max(0, parallel_steps),
        "estimated_time": estimated_time
    }


def analyze_function_library(pipeline):
    """Analyze the function library."""
    functions = pipeline.get_available_functions()
    
    # Group by category (simplified)
    categories = {}
    for func_name in functions:
        if any(word in func_name for word in ["read", "write", "file", "copy"]):
            category = "File Operations"
        elif any(word in func_name for word in ["send", "email", "api", "slack"]):
            category = "Communication"
        elif any(word in func_name for word in ["filter", "sort", "aggregate", "group"]):
            category = "Data Processing"
        elif any(word in func_name for word in ["execute", "insert", "update", "backup"]):
            category = "Database"
        elif any(word in func_name for word in ["scrape", "download", "monitor"]):
            category = "Web Scraping"
        else:
            category = "Other"
        
        categories.setdefault(category, []).append(func_name)
    
    print(f"📊 Total Functions: {len(functions)}")
    for category, funcs in categories.items():
        print(f"   {category}: {len(funcs)} functions")


def demonstrate_query_parsing(pipeline):
    """Demonstrate query parsing capabilities."""
    from parsers.query_parser import QueryParser
    
    parser = QueryParser()
    test_queries = [
        "Get all invoices from March and calculate the total",
        "Send a daily <NAME_EMAIL> every morning",
        "Monitor competitor prices and alert if they drop by 10%"
    ]
    
    for query in test_queries:
        parsed = parser.parse(query)
        print(f"\nQuery: {query}")
        print(f"   Type: {parsed.query_type.value}")
        print(f"   Intents: {[intent.value for intent in parsed.intents]}")
        print(f"   Keywords: {parsed.keywords[:5]}")  # First 5 keywords
        print(f"   Confidence: {parsed.confidence:.2f}")


if __name__ == "__main__":
    main()
