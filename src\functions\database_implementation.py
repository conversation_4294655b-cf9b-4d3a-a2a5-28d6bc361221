import logging
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Union


class MockDatabase:

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.query_history = []

    def execute_query(self, sql: str, table: str = None, **kwargs) -> Dict[str, Any]:
        try:
            self.logger.info(f"Executing query: {sql}")

            self.query_history.append({
                "sql": sql,
                "table": table,
                "timestamp": datetime.now().isoformat(),
                "parameters": kwargs
            })

            if not table:
                table = self._extract_table_from_sql(sql)

            if table == "invoices":
                data = self._generate_invoice_data(sql)
            elif table == "customers":
                data = self._generate_customer_data(sql)
            elif table == "orders":
                data = self._generate_order_data(sql)
            elif table == "transactions":
                data = self._generate_transaction_data(sql)
            else:
                data = [{"id": 1, "data": "sample data"}]

            return {
                "success": True,
                "data": data,
                "count": len(data),
                "sql": sql,
                "table": table
            }

        except Exception as e:
            self.logger.error(f"Query execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": [],
                "count": 0
            }
    
    def _extract_table_from_sql(self, sql: str) -> str:
        """Extract table name from SQL query."""
        sql_lower = sql.lower()
        
        if "from invoices" in sql_lower:
            return "invoices"
        elif "from customers" in sql_lower:
            return "customers"
        elif "from orders" in sql_lower:
            return "orders"
        elif "from transactions" in sql_lower:
            return "transactions"
        else:
            return "data"
    
    def _generate_invoice_data(self, sql: str) -> List[Dict[str, Any]]:
        """Generate realistic invoice data."""
        # Check for date filtering
        is_march = "month(invoice_date) = 03" in sql.lower() or "march" in sql.lower()
        is_year_filtered = "year(" in sql.lower()
        
        invoices = []
        base_date = datetime(2024, 3, 1) if is_march else datetime(2024, 1, 1)
        
        # Generate 15-20 invoices
        num_invoices = random.randint(15, 20)
        
        for i in range(1, num_invoices + 1):
            if is_march:
                # Generate March invoices
                invoice_date = base_date + timedelta(days=random.randint(0, 30))
            else:
                # Generate random invoices throughout the year
                invoice_date = base_date + timedelta(days=random.randint(0, 365))
            
            invoice = {
                "invoice_id": f"INV-{2024}-{i:04d}",
                "customer_id": f"CUST-{random.randint(1000, 9999)}",
                "customer_name": random.choice([
                    "Acme Corporation", "Tech Solutions Inc", "Global Industries Ltd",
                    "Innovation Labs", "Future Systems", "Digital Dynamics",
                    "Smart Solutions", "Enterprise Corp", "Advanced Tech"
                ]),
                "invoice_date": invoice_date.strftime("%Y-%m-%d"),
                "amount": round(random.uniform(500, 8000), 2),
                "status": random.choice(["paid", "pending", "overdue", "draft"]),
                "description": random.choice([
                    "Software License Annual Subscription",
                    "Consulting Services - Q1",
                    "Hardware Purchase - Servers",
                    "Maintenance Contract Renewal",
                    "Training Services Package",
                    "Support Package Premium",
                    "Cloud Services Monthly",
                    "Development Services"
                ]),
                "due_date": (invoice_date + timedelta(days=30)).strftime("%Y-%m-%d"),
                "tax_amount": round(random.uniform(50, 800), 2)
            }
            invoices.append(invoice)
        
        return invoices
    
    def _generate_customer_data(self, sql: str) -> List[Dict[str, Any]]:
        """Generate realistic customer data."""
        customers = [
            {
                "customer_id": "CUST-1001",
                "name": "Acme Corporation",
                "status": "active",
                "email": "<EMAIL>",
                "phone": "******-0101",
                "address": "123 Business Ave, Tech City, TC 12345",
                "created_date": "2023-01-15"
            },
            {
                "customer_id": "CUST-1002",
                "name": "Tech Solutions Inc",
                "status": "active",
                "email": "<EMAIL>",
                "phone": "******-0102",
                "address": "456 Innovation Blvd, Silicon Valley, SV 67890",
                "created_date": "2023-02-20"
            },
            {
                "customer_id": "CUST-1003",
                "name": "Global Industries Ltd",
                "status": "pending",
                "email": "<EMAIL>",
                "phone": "******-0103",
                "address": "789 Enterprise St, Business District, BD 54321",
                "created_date": "2023-03-10"
            },
            {
                "customer_id": "CUST-1004",
                "name": "Innovation Labs",
                "status": "active",
                "email": "<EMAIL>",
                "phone": "******-0104",
                "address": "321 Research Dr, Innovation Park, IP 98765",
                "created_date": "2023-04-05"
            },
            {
                "customer_id": "CUST-1005",
                "name": "Future Systems",
                "status": "inactive",
                "email": "<EMAIL>",
                "phone": "******-0105",
                "address": "654 Future Ln, Tomorrow City, TC 13579",
                "created_date": "2023-05-12"
            }
        ]
        
        # Filter by status if specified
        if "status = 'active'" in sql.lower():
            customers = [c for c in customers if c["status"] == "active"]
        elif "status = 'pending'" in sql.lower():
            customers = [c for c in customers if c["status"] == "pending"]
        elif "status = 'inactive'" in sql.lower():
            customers = [c for c in customers if c["status"] == "inactive"]
        
        return customers
    
    def _generate_order_data(self, sql: str) -> List[Dict[str, Any]]:
        """Generate realistic order data."""
        orders = []
        
        for i in range(1, 12):
            order_date = datetime(2024, 3, random.randint(1, 31))
            order = {
                "order_id": f"ORD-{i:04d}",
                "customer_id": f"CUST-{random.randint(1001, 1005)}",
                "order_date": order_date.strftime("%Y-%m-%d"),
                "amount": round(random.uniform(100, 3000), 2),
                "status": random.choice(["completed", "pending", "shipped", "cancelled"]),
                "items_count": random.randint(1, 10),
                "shipping_address": "Customer Address",
                "payment_method": random.choice(["credit_card", "bank_transfer", "paypal"])
            }
            orders.append(order)
        
        return orders
    
    def _generate_transaction_data(self, sql: str) -> List[Dict[str, Any]]:
        """Generate realistic transaction data."""
        transactions = []
        
        for i in range(1, 25):
            transaction_date = datetime(2024, 3, random.randint(1, 31))
            amount = round(random.uniform(50, 5000), 2)
            
            transaction = {
                "transaction_id": f"TXN-{i:06d}",
                "transaction_date": transaction_date.strftime("%Y-%m-%d"),
                "amount": amount,
                "type": random.choice(["payment", "refund", "charge", "transfer"]),
                "status": "completed",
                "reference": f"REF-{random.randint(100000, 999999)}",
                "description": random.choice([
                    "Invoice Payment",
                    "Refund Processing",
                    "Service Charge",
                    "Monthly Subscription",
                    "One-time Purchase"
                ])
            }
            transactions.append(transaction)
        
        return transactions
    
    def get_query_history(self) -> List[Dict[str, Any]]:
        """Get query execution history."""
        return self.query_history.copy()
    
    def clear_history(self):
        """Clear query history."""
        self.query_history.clear()


# Default database instance
_default_db = MockDatabase()


def execute_database_query(parameters: Dict[str, Any]) -> Dict[str, Any]:
    query = parameters.get("query", "")
    table = parameters.get("table", "")
    original_query = parameters.get("original_query", "")

    print(f"DATABASE QUERY:")
    print(f"   SQL: {query}")
    print(f"   Table: {table}")

    result = _default_db.execute_query(query, table, original_query=original_query)

    if result["success"]:
        data = result["data"]
        print(f"   Found {len(data)} records")

        if data:
            print(f"   Sample Results:")
            for i, record in enumerate(data[:3], 1):
                print(f"      {i}. {record}")
            if len(data) > 3:
                print(f"      ... and {len(data) - 3} more records")

    return {
        "success": result["success"],
        "error": result.get("error"),
        "result": result["data"],
        "count": result["count"],
        "query": query,
        "table": table
    }


def get_database() -> MockDatabase:
    return _default_db
