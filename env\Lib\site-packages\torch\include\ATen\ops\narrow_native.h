#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor narrow_symint(const at::Tensor & self, int64_t dim, c10::SymInt start, c10::SymInt length);
TORCH_API at::Tensor narrow_nested_symint(const at::Tensor & self, int64_t dim, c10::SymInt start, c10::SymInt length);
TORCH_API at::Tensor narrow_tensor_symint(const at::Tensor & self, int64_t dim, const at::Tensor & start, c10::SymInt length);
} // namespace native
} // namespace at
