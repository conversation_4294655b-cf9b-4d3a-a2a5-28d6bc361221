#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API masked_scatter_ {
  using schema = at::Tensor & (at::Tensor &, const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::masked_scatter_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "masked_scatter_(Tensor(a!) self, Tensor mask, Tensor source) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
};

struct TORCH_API masked_scatter {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::masked_scatter";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "masked_scatter(Tensor self, Tensor mask, Tensor source) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
};

struct TORCH_API masked_scatter_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, const at::Tensor &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::masked_scatter";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "masked_scatter.out(Tensor self, Tensor mask, Tensor source, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source, at::Tensor & out);
};

}} // namespace at::_ops
