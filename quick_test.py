#!/usr/bin/env python3
"""
Quick Test - Demonstrates that all imports and functionality work correctly.
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def main():
    """Quick demonstration of working functionality."""
    print("🚀 AI Function Call Pipeline - Quick Test")
    print("=" * 45)
    
    try:
        # Test 1: Import and use the simple pipeline
        print("1️⃣ Testing Simple Pipeline Import...")
        from simple_demo import SimplePipeline, ExecutionPlan, FunctionCall
        
        pipeline = SimplePipeline()
        print(f"✅ Pipeline created with {len(pipeline.get_available_functions())} functions")
        
        # Test 2: Process a query
        print("\n2️⃣ Testing Query Processing...")
        query = "Get sales data, analyze trends, and create a dashboard"
        result = pipeline.process_query(query)
        
        print(f"✅ Query processed successfully:")
        print(f"   Success: {result.success}")
        print(f"   Functions: {len(result.execution_plan.function_calls)}")
        print(f"   Confidence: {result.execution_plan.confidence:.2f}")
        
        # Test 3: Show execution plan
        print("\n3️⃣ Generated Execution Plan:")
        for i, call in enumerate(result.execution_plan.function_calls, 1):
            print(f"   {i}. {call.function_name}")
            print(f"      Parameters: {call.parameters}")
            print(f"      Reasoning: {call.reasoning}")
        
        # Test 4: Test function search
        print("\n4️⃣ Testing Function Search...")
        email_functions = pipeline.search_functions("email")
        data_functions = pipeline.search_functions("data")
        print(f"✅ Found {len(email_functions)} email functions")
        print(f"✅ Found {len(data_functions)} data functions")
        
        # Test 5: Test direct module imports
        print("\n5️⃣ Testing Direct Module Imports...")
        from functions.function_registry import FunctionRegistry
        from parsers.query_parser import QueryParser
        from models.model_interface import ModelConfig, DEFAULT_MODELS
        
        registry = FunctionRegistry()
        parser = QueryParser()
        config = ModelConfig(model_name="test/model")
        
        print(f"✅ Function Registry: {len(registry.get_all_functions())} functions")
        print(f"✅ Query Parser: Ready to parse queries")
        print(f"✅ Model Interface: {len(DEFAULT_MODELS)} default models available")
        
        print("\n🎉 All tests passed! The pipeline is fully functional.")
        print("\n💡 You can now use:")
        print("   python main_working.py 'Your query here'")
        print("   python main_working.py --interactive")
        print("   python run_tests.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ Everything is working perfectly!")
    else:
        print("\n⚠️ There were some issues.")
    
    sys.exit(0 if success else 1)
