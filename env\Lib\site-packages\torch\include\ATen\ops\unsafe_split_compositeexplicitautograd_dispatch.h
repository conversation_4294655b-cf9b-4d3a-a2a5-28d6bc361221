#pragma once
// @generated by torchgen/gen.py from DispatchKeyFunction.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {

namespace compositeexplicitautograd {

TORCH_API ::std::vector<at::Tensor> unsafe_split(const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> unsafe_split_symint(const at::Tensor & self, c10::SymInt split_size, int64_t dim=0);
TORCH_API void unsafe_split_out(at::TensorList out, const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API void unsafe_split_outf(const at::Tensor & self, int64_t split_size, int64_t dim, at::TensorList out);
TORCH_API void unsafe_split_symint_out(at::TensorList out, const at::Tensor & self, c10::SymInt split_size, int64_t dim=0);
TORCH_API void unsafe_split_symint_outf(const at::Tensor & self, c10::SymInt split_size, int64_t dim, at::TensorList out);

} // namespace compositeexplicitautograd
} // namespace at
