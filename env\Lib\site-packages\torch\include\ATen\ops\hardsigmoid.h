#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/hardsigmoid_ops.h>

namespace at {


// aten::hardsigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hardsigmoid_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::hardsigmoid_out::call(self, out);
}
// aten::hardsigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & hardsigmoid_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::hardsigmoid_out::call(self, out);
}

// aten::hardsigmoid(Tensor self) -> Tensor
inline at::Tensor hardsigmoid(const at::Tensor & self) {
    return at::_ops::hardsigmoid::call(self);
}

// aten::hardsigmoid_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & hardsigmoid_(at::Tensor & self) {
    return at::_ops::hardsigmoid_::call(self);
}

}
