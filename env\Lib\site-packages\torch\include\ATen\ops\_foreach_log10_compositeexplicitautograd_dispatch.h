#pragma once
// @generated by torchgen/gen.py from DispatchKeyFunction.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {

namespace compositeexplicitautograd {

TORCH_API ::std::vector<at::Tensor> _foreach_log10(at::TensorList self);
TORCH_API void _foreach_log10_out(at::TensorList out, at::TensorList self);
TORCH_API void _foreach_log10_outf(at::TensorList self, at::TensorList out);
TORCH_API void _foreach_log10_(at::TensorList self);

} // namespace compositeexplicitautograd
} // namespace at
