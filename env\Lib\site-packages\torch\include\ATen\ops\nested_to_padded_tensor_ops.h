#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API nested_to_padded_tensor {
  using schema = at::Tensor (const at::Tensor &, double, at::OptionalIntArrayRef);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nested_to_padded_tensor";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "nested_to_padded_tensor(Tensor self, float padding, int[]? output_size=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, double padding, at::OptionalIntArrayRef output_size);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double padding, at::OptionalIntArrayRef output_size);
};

}} // namespace at::_ops
