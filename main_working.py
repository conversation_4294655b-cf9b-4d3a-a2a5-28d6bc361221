#!/usr/bin/env python3
"""
AI Function Call Pipeline - Working Main Entry Point

This script provides a command-line interface for the AI Function Call Pipeline.
Uses the simple demo implementation to avoid import issues.
"""

import sys
import argparse
import json
from pathlib import Path

# Import the working simple demo
from simple_demo import SimplePipeline, FunctionCall, ExecutionPlan, PipelineResult


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="AI Function Call Pipeline - Process natural language queries into function call sequences"
    )
    
    parser.add_argument(
        "query",
        nargs="?",
        help="Natural language query to process"
    )
    
    parser.add_argument(
        "--list-functions",
        action="store_true",
        help="List all available functions"
    )
    
    parser.add_argument(
        "--search-functions",
        help="Search for functions by keyword"
    )
    
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Start interactive mode"
    )
    
    parser.add_argument(
        "--examples",
        action="store_true",
        help="Run example queries"
    )
    
    parser.add_argument(
        "--output",
        choices=["text", "json"],
        default="text",
        help="Output format"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Create pipeline
    print("🚀 AI Function Call Pipeline - Command Line Interface")
    print("=" * 55)
    
    try:
        pipeline = SimplePipeline()
        print("✅ Pipeline initialized successfully!")
        print(f"📚 Available functions: {len(pipeline.get_available_functions())}")
        
    except Exception as e:
        print(f"❌ Failed to initialize pipeline: {e}")
        return 1
    
    # Handle different modes
    if args.list_functions:
        list_functions(pipeline, args.output)
    elif args.search_functions:
        search_functions(pipeline, args.search_functions, args.output)
    elif args.examples:
        run_examples(pipeline, args.output)
    elif args.interactive:
        interactive_mode(pipeline, args.output)
    elif args.query:
        process_single_query(pipeline, args.query, args.output)
    else:
        print("\nNo action specified. Here are your options:")
        print("  python main_working.py --examples")
        print("  python main_working.py --interactive")
        print("  python main_working.py 'Your query here'")
        print("  python main_working.py --help")
    
    return 0


def list_functions(pipeline, output_format):
    """List all available functions."""
    functions = pipeline.get_available_functions()
    
    if output_format == "json":
        print(json.dumps({"functions": functions}, indent=2))
    else:
        print(f"\n📚 Available Functions ({len(functions)} total):")
        
        # Group by category
        categories = {}
        for func_name in functions:
            func_info = pipeline.functions[func_name]
            category = func_info["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(func_name)
        
        for category, funcs in categories.items():
            print(f"\n  📂 {category.replace('_', ' ').title()}:")
            for func in funcs:
                print(f"    • {func}")


def search_functions(pipeline, search_term, output_format):
    """Search for functions by keyword."""
    results = pipeline.search_functions(search_term)
    
    if output_format == "json":
        print(json.dumps({"search_term": search_term, "results": results}, indent=2))
    else:
        print(f"\n🔍 Search results for '{search_term}' ({len(results)} found):")
        for i, func in enumerate(results, 1):
            func_info = pipeline.functions[func]
            print(f"  {i}. {func}")
            print(f"     📝 {func_info['description']}")


def process_single_query(pipeline, query, output_format):
    """Process a single query."""
    print(f"\n🔍 Processing query: {query}")
    print("-" * 50)
    
    try:
        result = pipeline.process_query(query)
        
        if output_format == "json":
            output_data = {
                "query": query,
                "success": result.success,
                "execution_plan": {
                    "function_calls": [
                        {
                            "function_name": call.function_name,
                            "parameters": call.parameters,
                            "confidence": call.confidence,
                            "reasoning": call.reasoning
                        }
                        for call in result.execution_plan.function_calls
                    ],
                    "dependencies": result.execution_plan.dependencies,
                    "reasoning": result.execution_plan.reasoning,
                    "confidence": result.execution_plan.confidence
                }
            }
            
            if result.error_message:
                output_data["error"] = result.error_message
            
            print(json.dumps(output_data, indent=2))
        
        else:
            if result.success:
                plan = result.execution_plan
                print(f"✅ Processing successful!")
                print(f"📈 Confidence: {plan.confidence:.2f}")
                print(f"🔧 Functions: {len(plan.function_calls)}")
                
                if plan.function_calls:
                    print(f"\n📋 Execution Plan:")
                    for i, call in enumerate(plan.function_calls, 1):
                        print(f"  {i}. {call.function_name}")
                        print(f"     📥 Parameters: {call.parameters}")
                        print(f"     💭 Reasoning: {call.reasoning}")
                        print()
                
                if plan.dependencies:
                    print("🔗 Dependencies:")
                    for func, deps in plan.dependencies.items():
                        if deps:
                            print(f"  {func} ← {', '.join(deps)}")
                
                if plan.reasoning:
                    print(f"\n💡 Overall Reasoning: {plan.reasoning}")
            
            else:
                print(f"❌ Processing failed: {result.error_message}")
    
    except Exception as e:
        print(f"❌ Error: {e}")


def run_examples(pipeline, output_format):
    """Run example queries."""
    examples = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a report.",
        "Search for transactions over $1000 and create a visualization.",
        "Scrape competitor prices, update our database, and send alerts.",
        "Backup the database and notify the admin team."
    ]
    
    print("\n🚀 Running Example Queries:")
    print("=" * 50)
    
    for i, query in enumerate(examples, 1):
        print(f"\n{i}. Example Query:")
        print(f"   {query}")
        print("-" * 40)
        process_single_query(pipeline, query, output_format)


def interactive_mode(pipeline, output_format):
    """Start interactive mode."""
    print("\n🚀 AI Function Call Pipeline - Interactive Mode")
    print("Type 'help' for commands, 'quit' to exit")
    print("=" * 50)
    
    while True:
        try:
            query = input("\n💬 Enter your query > ").strip()
            
            if not query:
                continue
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if query.lower() == 'help':
                print_help()
                continue
            
            if query.startswith('search '):
                search_term = query[7:]
                search_functions(pipeline, search_term, output_format)
                continue
            
            if query == 'functions':
                list_functions(pipeline, output_format)
                continue
            
            if query == 'examples':
                run_examples(pipeline, output_format)
                continue
            
            # Process as regular query
            process_single_query(pipeline, query, output_format)
        
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def print_help():
    """Print help for interactive mode."""
    print("""
📖 Interactive Mode Commands:
  help                 - Show this help
  functions           - List all available functions
  search <term>       - Search functions by keyword
  examples            - Run example queries
  quit/exit/q         - Exit interactive mode
  
  Or enter any natural language query to process it.
  
📝 Example queries:
  • Get all users and send them an email
  • Analyze sales data and create a report
  • Backup database and notify admin
  • Filter customer data by status and generate report
  • Scrape competitor prices and update our database
    """)


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
