# AI Function Call Pipeline - User Guide

## Overview

The AI Function Call Pipeline is a sophisticated system that leverages open-source AI models to process natural language queries and generate structured sequences of function calls. This guide will help you understand how to use and extend the pipeline.

## Quick Start

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-function-call-pipeline
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run basic example**
   ```bash
   python examples/basic_example.py
   ```

### Basic Usage

```python
from src.pipeline.core import FunctionCallPipeline, PipelineBuilder
from src.models.model_interface import DEFAULT_MODELS

# Create pipeline
pipeline = (PipelineBuilder()
           .with_model(DEFAULT_MODELS["mistral-7b"])
           .with_execution(False)  # Safe mode
           .build())

# Process query
query = "Retrieve all invoices for March, summarize the total amount, and send the summary to my email."
result = pipeline.process_query(query)

# Check results
if result.success:
    print(f"Functions to execute: {len(result.execution_plan.function_calls)}")
    for call in result.execution_plan.function_calls:
        print(f"- {call.function_name}({call.parameters})")
```

## Core Components

### 1. Function Registry

The function registry contains ~50 predefined functions across various categories:

- **Data Processing**: filter_data, sort_data, aggregate_data, group_data
- **File Operations**: read_file, write_file, copy_file, delete_file
- **Communication**: send_email, send_sms, make_api_call, post_to_slack
- **Database**: execute_query, insert_record, update_record, backup_database
- **Analytics**: calculate_statistics, generate_report, create_visualization
- **And more...**

#### Adding Custom Functions

```python
from src.functions.function_registry import FunctionRegistry, FunctionDefinition, FunctionParameter, FunctionCategory

# Create custom function
custom_function = FunctionDefinition(
    name="my_custom_function",
    description="Does something custom",
    category=FunctionCategory.DATA_PROCESSING,
    parameters=[
        FunctionParameter("input_data", "list", "Input data to process"),
        FunctionParameter("option", "str", "Processing option", False, "default")
    ],
    returns="dict",
    examples=["Process user data", "Transform records"]
)

# Register function
registry = FunctionRegistry()
registry.register_function(custom_function)
```

### 2. AI Model Integration

The pipeline supports multiple open-source models:

- **Mistral 7B** (default): `mistralai/Mistral-7B-Instruct-v0.2`
- **Llama 2 7B**: `meta-llama/Llama-2-7b-chat-hf`
- **Phi-3**: `microsoft/Phi-3-mini-4k-instruct`
- **Gemma 7B**: `google/gemma-7b-it`

#### Model Configuration

```yaml
# config/model_config.yaml
models:
  custom-model:
    model_name: "your-org/your-model"
    device: "auto"
    max_tokens: 2048
    temperature: 0.7
    top_p: 0.9
```

### 3. Query Parser

The query parser extracts structure from natural language:

```python
from src.parsers.query_parser import QueryParser

parser = QueryParser()
parsed = parser.parse("Send daily reports to the team")

print(f"Query type: {parsed.query_type}")
print(f"Intents: {parsed.intents}")
print(f"Entities: {parsed.entities}")
print(f"Keywords: {parsed.keywords}")
```

### 4. Execution Engine

The execution engine handles function execution with dependency management:

```python
from src.utils.execution_engine import ExecutionEngine, MockExecutionEngine

# Use mock engine for safety
engine = MockExecutionEngine()

# Execute plan
result = engine.execute_plan(execution_plan)
print(f"Execution successful: {result.success}")
print(f"Total time: {result.total_time:.2f}s")
```

## Configuration

### Environment Variables

- `PIPELINE_MODEL`: Override default model
- `PIPELINE_DEVICE`: Force device (cpu/cuda)
- `PIPELINE_ENABLE_EXECUTION`: Enable actual function execution
- `PIPELINE_MOCK_MODE`: Use mock implementations

### Configuration File

Edit `config/model_config.yaml` to customize:

```yaml
# Model settings
default_model: "mistral-7b"

# Pipeline settings
pipeline:
  enable_execution: false
  max_workers: 4
  execution_timeout: 300.0

# Logging
logging:
  level: "INFO"
  file: "logs/pipeline.log"
```

## Advanced Usage

### Custom Model Implementation

```python
from src.models.model_interface import ModelInterface, ModelResponse

class CustomModel(ModelInterface):
    def generate_response(self, prompt: str, **kwargs) -> ModelResponse:
        # Your custom model implementation
        pass
    
    def extract_function_calls(self, query: str, available_functions: List[str]) -> List[FunctionCall]:
        # Your function extraction logic
        pass
    
    def create_execution_plan(self, query: str, function_calls: List[FunctionCall]) -> ExecutionPlan:
        # Your execution planning logic
        pass

# Register custom model
from src.models.model_interface import ModelFactory
ModelFactory.register_model("custom", CustomModel)
```

### Pipeline Customization

```python
# Create custom pipeline
pipeline = (PipelineBuilder()
           .with_model(custom_model_config)
           .with_functions(custom_registry)
           .with_execution(True)
           .build())

# Add custom validation
def custom_validator(execution_plan):
    # Your validation logic
    return {"valid": True}

pipeline._validate_execution_plan = custom_validator
```

## Safety and Security

### Default Safety Measures

1. **Execution Disabled by Default**: Functions are not executed unless explicitly enabled
2. **Mock Mode**: Safe mock implementations for testing
3. **Validation**: Execution plans are validated before execution
4. **Dependency Checking**: Circular dependencies are detected and prevented

### Enabling Execution

⚠️ **Warning**: Only enable execution in trusted environments with proper security measures.

```python
# Enable execution (use with caution)
pipeline = (PipelineBuilder()
           .with_execution(True)
           .build())

# Or via configuration
config["pipeline"]["enable_execution"] = True
```

### Security Best Practices

1. **Validate Inputs**: Always validate user inputs
2. **Sandbox Execution**: Run in isolated environments
3. **Audit Logs**: Monitor all function executions
4. **Access Controls**: Implement proper authentication
5. **Rate Limiting**: Prevent abuse

## Troubleshooting

### Common Issues

1. **Model Loading Fails**
   - Check GPU memory availability
   - Try CPU mode: `device: "cpu"`
   - Use smaller model: `phi-3`

2. **Function Not Found**
   - Check function registry: `pipeline.get_available_functions()`
   - Verify function name spelling
   - Register custom functions if needed

3. **Low Confidence Scores**
   - Improve query clarity
   - Add more context
   - Check function descriptions

4. **Dependency Errors**
   - Review execution plan dependencies
   - Check for circular dependencies
   - Validate function parameters

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging
config["logging"]["level"] = "DEBUG"
```

## Performance Optimization

### Model Optimization

1. **Use GPU**: Set `device: "cuda"` for faster inference
2. **Quantization**: Use `torch_dtype: "float16"` to reduce memory
3. **Batch Processing**: Process multiple queries together

### Pipeline Optimization

1. **Function Caching**: Cache function results
2. **Parallel Execution**: Enable parallel function execution
3. **Model Caching**: Keep model loaded in memory

## Examples

See the `examples/` directory for comprehensive examples:

- `basic_example.py`: Simple usage patterns
- `advanced_example.py`: Complex scenarios and workflows

## API Reference

For detailed API documentation, see:
- `docs/API_REFERENCE.md`
- Inline documentation in source code
- Type hints for all public interfaces

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

## License

MIT License - see LICENSE file for details.
