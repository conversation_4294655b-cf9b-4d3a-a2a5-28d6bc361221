# AI Function Call Pipeline Configuration

# Default model configuration
default_model: "mistral-7b"

# Available models
models:
  mistral-7b:
    model_name: "mistralai/Mistral-7B-Instruct-v0.2"
    device: "auto"  # auto, cpu, cuda
    max_tokens: 2048
    temperature: 0.7
    top_p: 0.9
    torch_dtype: "float16"  # float16, float32
    
  llama2-7b:
    model_name: "meta-llama/Llama-2-7b-chat-hf"
    device: "auto"
    max_tokens: 2048
    temperature: 0.7
    top_p: 0.9
    torch_dtype: "float16"
    
  phi-3:
    model_name: "microsoft/Phi-3-mini-4k-instruct"
    device: "auto"
    max_tokens: 2048
    temperature: 0.7
    top_p: 0.9
    torch_dtype: "float16"
    
  gemma-7b:
    model_name: "google/gemma-7b-it"
    device: "auto"
    max_tokens: 2048
    temperature: 0.7
    top_p: 0.9
    torch_dtype: "float16"

# Pipeline configuration
pipeline:
  enable_execution: false  # Set to true to actually execute functions
  max_workers: 4
  execution_timeout: 300.0  # seconds
  validation_enabled: true
  
# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/pipeline.log"
  
# Function registry configuration
functions:
  auto_register: true
  custom_functions_path: "custom_functions/"
  
# Query parser configuration
parser:
  confidence_threshold: 0.5
  max_keywords: 20
  enable_entity_extraction: true
  
# Execution engine configuration
execution:
  mock_mode: true  # Use mock implementations for safety
  parallel_execution: false
  retry_attempts: 3
  retry_delay: 1.0  # seconds
