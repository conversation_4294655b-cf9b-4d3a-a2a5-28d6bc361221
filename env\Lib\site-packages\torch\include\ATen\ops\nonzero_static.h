#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/nonzero_static_ops.h>

namespace at {


// aten::nonzero_static.out(Tensor self, *, SymInt size, int fill_value=-1, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & nonzero_static_out(at::Tensor & out, const at::Tensor & self, int64_t size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & nonzero_static_out(at::Tensor & out, const at::Tensor & self, int64_t size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
  }
}

// aten::nonzero_static.out(Tensor self, *, SymInt size, int fill_value=-1, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & nonzero_static_outf(const at::Tensor & self, int64_t size, int64_t fill_value, at::Tensor & out) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & nonzero_static_outf(const at::Tensor & self, int64_t size, int64_t fill_value, at::Tensor & out) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
  }
}

// aten::nonzero_static.out(Tensor self, *, SymInt size, int fill_value=-1, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & nonzero_static_symint_out(at::Tensor & out, const at::Tensor & self, c10::SymInt size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & nonzero_static_out(at::Tensor & out, const at::Tensor & self, c10::SymInt size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
  }
}

// aten::nonzero_static.out(Tensor self, *, SymInt size, int fill_value=-1, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & nonzero_static_symint_outf(const at::Tensor & self, c10::SymInt size, int64_t fill_value, at::Tensor & out) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & nonzero_static_outf(const at::Tensor & self, c10::SymInt size, int64_t fill_value, at::Tensor & out) {
    return at::_ops::nonzero_static_out::call(self, size, fill_value, out);
  }
}

// aten::nonzero_static(Tensor self, *, SymInt size, int fill_value=-1) -> Tensor
inline at::Tensor nonzero_static(const at::Tensor & self, int64_t size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static::call(self, size, fill_value);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor nonzero_static(const at::Tensor & self, int64_t size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static::call(self, size, fill_value);
  }
}

// aten::nonzero_static(Tensor self, *, SymInt size, int fill_value=-1) -> Tensor
inline at::Tensor nonzero_static_symint(const at::Tensor & self, c10::SymInt size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static::call(self, size, fill_value);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor nonzero_static(const at::Tensor & self, c10::SymInt size, int64_t fill_value=-1) {
    return at::_ops::nonzero_static::call(self, size, fill_value);
  }
}

}
