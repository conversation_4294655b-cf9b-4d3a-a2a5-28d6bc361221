"""
Configuration Loader - Loads and manages configuration settings.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path

from ..models.model_interface import ModelConfig


class ConfigLoader:
    """Loads and manages configuration from YAML files."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the config loader.
        
        Args:
            config_path: Path to the configuration file
        """
        if config_path is None:
            # Default to config/model_config.yaml relative to project root
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "model_config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # Apply environment variable overrides
            config = self._apply_env_overrides(config)
            
            return config
            
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        # Override default model if specified
        if "PIPELINE_MODEL" in os.environ:
            config["default_model"] = os.environ["PIPELINE_MODEL"]
        
        # Override device if specified
        if "PIPELINE_DEVICE" in os.environ:
            device = os.environ["PIPELINE_DEVICE"]
            for model_config in config.get("models", {}).values():
                model_config["device"] = device
        
        # Override execution mode
        if "PIPELINE_ENABLE_EXECUTION" in os.environ:
            enable_execution = os.environ["PIPELINE_ENABLE_EXECUTION"].lower() == "true"
            config.setdefault("pipeline", {})["enable_execution"] = enable_execution
        
        # Override mock mode
        if "PIPELINE_MOCK_MODE" in os.environ:
            mock_mode = os.environ["PIPELINE_MOCK_MODE"].lower() == "true"
            config.setdefault("execution", {})["mock_mode"] = mock_mode
        
        return config
    
    def get_model_config(self, model_name: Optional[str] = None) -> ModelConfig:
        """
        Get model configuration.
        
        Args:
            model_name: Name of the model, uses default if None
            
        Returns:
            ModelConfig object
        """
        if model_name is None:
            model_name = self.config.get("default_model", "mistral-7b")
        
        models = self.config.get("models", {})
        if model_name not in models:
            raise ValueError(f"Model '{model_name}' not found in configuration")
        
        model_config = models[model_name]
        return ModelConfig(**model_config)
    
    def get_pipeline_config(self) -> Dict[str, Any]:
        """Get pipeline configuration."""
        return self.config.get("pipeline", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get("logging", {})
    
    def get_execution_config(self) -> Dict[str, Any]:
        """Get execution configuration."""
        return self.config.get("execution", {})
    
    def get_parser_config(self) -> Dict[str, Any]:
        """Get parser configuration."""
        return self.config.get("parser", {})
    
    def get_functions_config(self) -> Dict[str, Any]:
        """Get functions configuration."""
        return self.config.get("functions", {})
    
    def get_available_models(self) -> list:
        """Get list of available model names."""
        return list(self.config.get("models", {}).keys())
    
    def is_execution_enabled(self) -> bool:
        """Check if function execution is enabled."""
        return self.get_pipeline_config().get("enable_execution", False)
    
    def is_mock_mode(self) -> bool:
        """Check if mock mode is enabled."""
        return self.get_execution_config().get("mock_mode", True)
    
    def update_config(self, updates: Dict[str, Any]):
        """Update configuration with new values."""
        self._deep_update(self.config, updates)
    
    def save_config(self, path: Optional[str] = None):
        """Save current configuration to file."""
        save_path = Path(path) if path else self.config_path
        
        with open(save_path, 'w', encoding='utf-8') as file:
            yaml.dump(self.config, file, default_flow_style=False, indent=2)
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """Deep update dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value


def setup_logging(config_loader: ConfigLoader):
    """Setup logging based on configuration."""
    import logging
    import os
    
    logging_config = config_loader.get_logging_config()
    
    # Create logs directory if it doesn't exist
    log_file = logging_config.get("file", "logs/pipeline.log")
    log_dir = os.path.dirname(log_file)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
    
    # Configure logging
    level = getattr(logging, logging_config.get("level", "INFO").upper())
    format_str = logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)


# Global config loader instance
_config_loader = None


def get_config_loader() -> ConfigLoader:
    """Get the global config loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


def reload_config():
    """Reload the global configuration."""
    global _config_loader
    _config_loader = ConfigLoader()
