#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_pad_circular_ops.h>

namespace at {


// aten::_pad_circular(Tensor self, SymInt[] pad) -> Tensor
inline at::Tensor _pad_circular(const at::Tensor & self, at::IntArrayRef pad) {
    return at::_ops::_pad_circular::call(self, c10::fromIntArrayRefSlow(pad));
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor _pad_circular(const at::Tensor & self, at::IntArrayRef pad) {
    return at::_ops::_pad_circular::call(self, c10::fromIntArrayRefSlow(pad));
  }
}

// aten::_pad_circular(Tensor self, SymInt[] pad) -> Tensor
inline at::Tensor _pad_circular_symint(const at::Tensor & self, c10::SymIntArrayRef pad) {
    return at::_ops::_pad_circular::call(self, pad);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor _pad_circular(const at::Tensor & self, c10::SymIntArrayRef pad) {
    return at::_ops::_pad_circular::call(self, pad);
  }
}

}
