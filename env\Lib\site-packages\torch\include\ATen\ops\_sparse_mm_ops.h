#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _sparse_mm {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_sparse_mm";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_sparse_mm(Tensor sparse, Tensor dense) -> Tensor";
  static at::Tensor call(const at::Tensor & sparse, const at::Tensor & dense);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & sparse, const at::Tensor & dense);
};

struct TORCH_API _sparse_mm_reduce {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, c10::string_view);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_sparse_mm";
  static constexpr const char* overload_name = "reduce";
  static constexpr const char* schema_str = "_sparse_mm.reduce(Tensor sparse, Tensor dense, str reduce) -> Tensor";
  static at::Tensor call(const at::Tensor & sparse, const at::Tensor & dense, c10::string_view reduce);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & sparse, const at::Tensor & dense, c10::string_view reduce);
};

}} // namespace at::_ops
