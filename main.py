#!/usr/bin/env python3
"""
AI Function Call Pipeline - Main Entry Point

This script provides a command-line interface for the AI Function Call Pipeline.
"""

import sys
import argparse
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from pipeline.core import FunctionCallPipeline, PipelineBuilder
from models.model_interface import DEFAULT_MODELS
from utils.config_loader import ConfigLoader, setup_logging


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="AI Function Call Pipeline - Process natural language queries into function call sequences"
    )
    
    parser.add_argument(
        "query",
        nargs="?",
        help="Natural language query to process"
    )
    
    parser.add_argument(
        "--model",
        default=None,
        help="Model to use (mistral-7b, llama2-7b, phi-3, gemma-7b)"
    )
    
    parser.add_argument(
        "--execute",
        action="store_true",
        help="Actually execute functions (use with caution)"
    )
    
    parser.add_argument(
        "--mock",
        action="store_true",
        default=True,
        help="Use mock execution (default: True)"
    )
    
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--list-functions",
        action="store_true",
        help="List all available functions"
    )
    
    parser.add_argument(
        "--search-functions",
        help="Search for functions by keyword"
    )
    
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Start interactive mode"
    )
    
    parser.add_argument(
        "--examples",
        action="store_true",
        help="Run example queries"
    )
    
    parser.add_argument(
        "--output",
        choices=["text", "json"],
        default="text",
        help="Output format"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Setup configuration and logging
    config_loader = ConfigLoader(args.config)
    setup_logging(config_loader)
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create pipeline
    try:
        model_config = config_loader.get_model_config(args.model)

        pipeline = (PipelineBuilder()
                   .with_model(model_config)
                   .with_execution(args.execute and not args.mock)
                   .build())

        print(f"✅ Pipeline initialized with model: {model_config.model_name}")

    except Exception as e:
        print(f"❌ Failed to initialize pipeline: {e}")
        print("💡 Falling back to simple demo mode...")

        # Create simple demo pipeline
        from simple_demo import SimplePipeline
        pipeline = SimplePipeline()
    
    # Handle different modes
    if args.list_functions:
        list_functions(pipeline, args.output)
    elif args.search_functions:
        search_functions(pipeline, args.search_functions, args.output)
    elif args.examples:
        run_examples(pipeline, args.output)
    elif args.interactive:
        interactive_mode(pipeline, args.output)
    elif args.query:
        process_single_query(pipeline, args.query, args.output, args.execute)
    else:
        print("No action specified. Use --help for usage information.")
        print("Try: python main.py --examples")


def list_functions(pipeline, output_format):
    """List all available functions."""
    functions = pipeline.get_available_functions()
    
    if output_format == "json":
        print(json.dumps({"functions": functions}, indent=2))
    else:
        print(f"📚 Available Functions ({len(functions)} total):")
        for i, func in enumerate(functions, 1):
            print(f"  {i:2d}. {func}")


def search_functions(pipeline, search_term, output_format):
    """Search for functions by keyword."""
    results = pipeline.search_functions(search_term)
    
    if output_format == "json":
        print(json.dumps({"search_term": search_term, "results": results}, indent=2))
    else:
        print(f"🔍 Search results for '{search_term}' ({len(results)} found):")
        for i, func in enumerate(results, 1):
            print(f"  {i}. {func}")


def process_single_query(pipeline, query, output_format, execute):
    """Process a single query."""
    print(f"🔍 Processing query: {query}")
    
    try:
        result = pipeline.process_query(query, execute=execute)
        
        if output_format == "json":
            output_data = {
                "query": query,
                "success": result.success,
                "execution_plan": {
                    "function_calls": [
                        {
                            "function_name": call.function_name,
                            "parameters": call.parameters,
                            "confidence": call.confidence,
                            "reasoning": call.reasoning
                        }
                        for call in result.execution_plan.function_calls
                    ],
                    "dependencies": result.execution_plan.dependencies,
                    "reasoning": result.execution_plan.reasoning,
                    "confidence": result.execution_plan.confidence
                }
            }
            
            if result.error_message:
                output_data["error"] = result.error_message
            
            if result.execution_results:
                output_data["execution_results"] = result.execution_results
            
            print(json.dumps(output_data, indent=2))
        
        else:
            if result.success:
                print("✅ Processing successful!")
                print(f"📈 Confidence: {result.execution_plan.confidence:.2f}")
                print(f"🔧 Functions: {len(result.execution_plan.function_calls)}")
                
                if result.execution_plan.function_calls:
                    print("\n📋 Execution Plan:")
                    for i, call in enumerate(result.execution_plan.function_calls, 1):
                        print(f"  {i}. {call.function_name}({call.parameters})")
                        if call.reasoning:
                            print(f"     💭 {call.reasoning}")
                
                if result.execution_plan.dependencies:
                    print("\n🔗 Dependencies:")
                    for func, deps in result.execution_plan.dependencies.items():
                        if deps:
                            print(f"  {func} ← {', '.join(deps)}")
                
                if result.execution_plan.reasoning:
                    print(f"\n💡 Reasoning: {result.execution_plan.reasoning}")
                
                if result.execution_results:
                    print(f"\n🎯 Execution Results:")
                    for i, exec_result in enumerate(result.execution_results, 1):
                        print(f"  {i}. {exec_result}")
            
            else:
                print(f"❌ Processing failed: {result.error_message}")
    
    except Exception as e:
        print(f"❌ Error: {e}")


def run_examples(pipeline, output_format):
    """Run example queries."""
    examples = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a report.",
        "Search for transactions over $1000 and create a visualization.",
    ]
    
    print("🚀 Running Example Queries:")
    print("=" * 50)
    
    for i, query in enumerate(examples, 1):
        print(f"\n{i}. {query}")
        print("-" * 40)
        process_single_query(pipeline, query, output_format, False)


def interactive_mode(pipeline, output_format):
    """Start interactive mode."""
    print("🚀 AI Function Call Pipeline - Interactive Mode")
    print("Type 'help' for commands, 'quit' to exit")
    print("=" * 50)
    
    while True:
        try:
            query = input("\n> ").strip()
            
            if not query:
                continue
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if query.lower() == 'help':
                print_help()
                continue
            
            if query.startswith('search '):
                search_term = query[7:]
                search_functions(pipeline, search_term, output_format)
                continue
            
            if query == 'functions':
                list_functions(pipeline, output_format)
                continue
            
            # Process as regular query
            process_single_query(pipeline, query, output_format, False)
        
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def print_help():
    """Print help for interactive mode."""
    print("""
📖 Interactive Mode Commands:
  help                 - Show this help
  functions           - List all available functions
  search <term>       - Search functions by keyword
  quit/exit/q         - Exit interactive mode
  
  Or enter any natural language query to process it.
  
📝 Example queries:
  - Get all users and send them an email
  - Analyze sales data and create a report
  - Backup database and notify admin
    """)


if __name__ == "__main__":
    main()
