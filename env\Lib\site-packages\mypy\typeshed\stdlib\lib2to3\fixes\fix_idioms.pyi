from typing import ClassVar, Final, Literal

from .. import fixer_base

CMP: Final[str]
TYPE: Final[str]

class FixIdioms(fixer_base.BaseFix):
    BM_compatible: ClassVar[Literal[False]]
    PATTERN: ClassVar[str]
    def match(self, node): ...
    def transform(self, node, results): ...
    def transform_isinstance(self, node, results): ...
    def transform_while(self, node, results) -> None: ...
    def transform_sort(self, node, results) -> None: ...
