#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API cosine_similarity {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, int64_t, double);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::cosine_similarity";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "cosine_similarity(Tensor x1, Tensor x2, int dim=1, float eps=1e-08) -> Tensor";
  static at::Tensor call(const at::Tensor & x1, const at::Tensor & x2, int64_t dim, double eps);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & x1, const at::Tensor & x2, int64_t dim, double eps);
};

}} // namespace at::_ops
