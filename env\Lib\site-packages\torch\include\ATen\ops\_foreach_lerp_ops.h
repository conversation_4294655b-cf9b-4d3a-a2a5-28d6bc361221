#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _foreach_lerp_List {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::TensorList, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "List";
  static constexpr const char* schema_str = "_foreach_lerp.List(Tensor[] self, Tensor[] tensors1, Tensor[] weights) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::TensorList tensors1, at::TensorList weights);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::TensorList weights);
};

struct TORCH_API _foreach_lerp__List {
  using schema = void (at::TensorList, at::TensorList, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp_";
  static constexpr const char* overload_name = "List";
  static constexpr const char* schema_str = "_foreach_lerp_.List(Tensor(a!)[] self, Tensor[] tensors1, Tensor[] weights) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, at::TensorList weights);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::TensorList weights);
};

struct TORCH_API _foreach_lerp_Scalar {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "Scalar";
  static constexpr const char* schema_str = "_foreach_lerp.Scalar(Tensor[] self, Tensor[] tensors1, Scalar weight) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::TensorList tensors1, const at::Scalar & weight);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, const at::Scalar & weight);
};

struct TORCH_API _foreach_lerp__Scalar {
  using schema = void (at::TensorList, at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp_";
  static constexpr const char* overload_name = "Scalar";
  static constexpr const char* schema_str = "_foreach_lerp_.Scalar(Tensor(a!)[] self, Tensor[] tensors1, Scalar weight) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, const at::Scalar & weight);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, const at::Scalar & weight);
};

struct TORCH_API _foreach_lerp_ScalarList {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::TensorList, at::ArrayRef<at::Scalar>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "ScalarList";
  static constexpr const char* schema_str = "_foreach_lerp.ScalarList(Tensor[] self, Tensor[] tensors1, Scalar[] weight) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight);
};

struct TORCH_API _foreach_lerp__ScalarList {
  using schema = void (at::TensorList, at::TensorList, at::ArrayRef<at::Scalar>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp_";
  static constexpr const char* overload_name = "ScalarList";
  static constexpr const char* schema_str = "_foreach_lerp_.ScalarList(Tensor(a!)[] self, Tensor[] tensors1, Scalar[] weight) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight);
};

struct TORCH_API _foreach_lerp_List_out {
  using schema = void (at::TensorList, at::TensorList, at::TensorList, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "List_out";
  static constexpr const char* schema_str = "_foreach_lerp.List_out(Tensor[] self, Tensor[] tensors1, Tensor[] weights, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, at::TensorList weights, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::TensorList weights, at::TensorList out);
};

struct TORCH_API _foreach_lerp_Scalar_out {
  using schema = void (at::TensorList, at::TensorList, const at::Scalar &, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "Scalar_out";
  static constexpr const char* schema_str = "_foreach_lerp.Scalar_out(Tensor[] self, Tensor[] tensors1, Scalar weight, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, const at::Scalar & weight, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, const at::Scalar & weight, at::TensorList out);
};

struct TORCH_API _foreach_lerp_ScalarList_out {
  using schema = void (at::TensorList, at::TensorList, at::ArrayRef<at::Scalar>, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_lerp";
  static constexpr const char* overload_name = "ScalarList_out";
  static constexpr const char* schema_str = "_foreach_lerp.ScalarList_out(Tensor[] self, Tensor[] tensors1, Scalar[] weight, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList tensors1, at::ArrayRef<at::Scalar> weight, at::TensorList out);
};

}} // namespace at::_ops
